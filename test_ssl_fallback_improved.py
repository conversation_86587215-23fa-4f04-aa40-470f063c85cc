#!/usr/bin/env python3
"""
测试改进的SSL降级逻辑
"""

import socket
import ssl
import time
import requests

# 代理配置
PROXY_HOST = '127.0.0.1'
PROXY_PORT = 8888

def test_https_with_fallback():
    """测试HTTPS请求的SSL降级"""
    print("\n=== 测试HTTPS SSL降级逻辑 ===")
    
    test_domains = [
        'httpbin.org',
        'www.google.com',
        'api.github.com'
    ]
    
    for domain in test_domains:
        try:
            print(f"\n🔐 测试域名: {domain}")
            
            # 连接到代理
            proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            proxy_socket.settimeout(15)
            proxy_socket.connect((PROXY_HOST, PROXY_PORT))
            
            # 发送CONNECT请求
            connect_request = f"CONNECT {domain}:443 HTTP/1.1\r\n"
            connect_request += f"Host: {domain}:443\r\n"
            connect_request += "User-Agent: FallbackTest/1.0\r\n"
            connect_request += "Proxy-Connection: keep-alive\r\n"
            connect_request += "\r\n"
            
            print(f"📤 发送CONNECT请求")
            proxy_socket.send(connect_request.encode())
            
            # 读取代理响应
            response = proxy_socket.recv(1024).decode()
            print(f"📨 代理响应: {response.strip()}")
            
            if "200" in response:
                print("✅ CONNECT成功")
                
                # 尝试SSL握手
                try:
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE
                    
                    ssl_socket = ssl_context.wrap_socket(proxy_socket, server_hostname=domain)
                    print("✅ SSL握手成功")
                    
                    # 发送HTTP请求
                    http_request = f"GET / HTTP/1.1\r\n"
                    http_request += f"Host: {domain}\r\n"
                    http_request += "User-Agent: FallbackTest/1.0\r\n"
                    http_request += "Connection: close\r\n"
                    http_request += "\r\n"
                    
                    ssl_socket.send(http_request.encode())
                    
                    # 读取响应
                    response_data = ssl_socket.recv(4096)
                    response_text = response_data.decode('utf-8', errors='ignore')
                    lines = response_text.split('\n')
                    if lines:
                        print(f"📨 HTTP响应: {lines[0]}")
                    
                    ssl_socket.close()
                    
                except Exception as e:
                    print(f"❌ SSL/HTTP错误: {e}")
                    print("💡 应该触发降级到隧道模式")
                    proxy_socket.close()
            else:
                print("❌ CONNECT失败")
                proxy_socket.close()
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
        
        time.sleep(2)  # 等待一下再测试下一个

def test_http_requests():
    """测试HTTP请求（应该正常工作）"""
    print("\n=== 测试HTTP请求 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    http_urls = [
        'http://httpbin.org/get',
        'http://httpbin.org/json'
    ]
    
    for url in http_urls:
        try:
            print(f"📡 HTTP请求: {url}")
            response = requests.get(url, proxies=proxies, timeout=10)
            print(f"✅ 状态码: {response.status_code}, 大小: {len(response.content)} 字节")
        except Exception as e:
            print(f"❌ HTTP请求失败: {e}")
        time.sleep(1)

def test_browser_like_requests():
    """模拟浏览器请求"""
    print("\n=== 模拟浏览器HTTPS请求 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    test_urls = [
        'https://httpbin.org/get',
        'https://www.baidu.com'
    ]
    
    for url in test_urls:
        try:
            print(f"🌐 浏览器请求: {url}")
            response = requests.get(
                url, 
                proxies=proxies, 
                headers=headers,
                timeout=15,
                verify=False
            )
            print(f"✅ 状态码: {response.status_code}")
            print(f"📊 响应大小: {len(response.content)} 字节")
            
        except Exception as e:
            print(f"❌ 浏览器请求失败: {e}")
        
        time.sleep(2)

if __name__ == "__main__":
    print("🚀 开始测试改进的SSL降级逻辑")
    print(f"代理地址: {PROXY_HOST}:{PROXY_PORT}")
    print("=" * 60)
    
    # 等待代理启动
    time.sleep(2)
    
    # 测试HTTP请求（基准测试）
    test_http_requests()
    
    # 测试HTTPS SSL降级
    test_https_with_fallback()
    
    # 测试浏览器类型的请求
    test_browser_like_requests()
    
    print("\n" + "=" * 60)
    print("🎯 SSL降级逻辑测试完成！")
    print("\n请检查PacketCapture日志：")
    print("1. 🔄 是否看到改进的降级日志")
    print("2. ✅ CONNECT响应是否发送成功")
    print("3. 🔗 隧道连接是否正确建立")
    print("4. ⚠️ SSL握手异常是否被正确处理")
    print("5. 📊 流量统计是否正常工作")
