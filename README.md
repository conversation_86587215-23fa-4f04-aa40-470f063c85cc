# 抓包SDK (Packet Capture SDK)

企业级网络流量抓取与分析SDK，支持HTTP/HTTPS、WebSocket和MQTT协议，具备SSL/TLS解密、流量回调、Mock数据等高级功能。

## 功能特性

- **多协议支持**: HTTP/HTTPS、WebSocket/WSS、MQTT/MQTTS
- **SSL/TLS解密**: 中间人解密能力，支持自定义证书
- **流量回调**: 实时获取网络流量数据
- **Mock引擎**: 基于规则的请求模拟响应
- **降级处理**: SSL失败时自动切换隧道模式
- **企业级特性**: 数据脱敏、安全审计、性能监控

## 技术栈

- **核心语言**: Kotlin 1.9+
- **网络框架**: Netty 4.1+
- **依赖注入**: Koin 3.4+
- **异步处理**: Kotlin Coroutines
- **数据序列化**: Kotlin Serialization
- **加密安全**: Bouncy Castle
- **日志系统**: SLF4J + Logback

## 快速开始

### 1. 添加依赖

```kotlin
dependencies {
    implementation("com.company.sdk:packet-capture-sdk:1.0.0")
}
```

### 2. 基本使用

```kotlin
// 创建配置
val config = SDKConfig(
    port = 8888,
    protocols = setOf(Protocol.HTTPS, Protocol.WEBSOCKET, Protocol.MQTT),
    enableSslMitm = true
)

// 创建SDK实例
val sdk = PacketCaptureSDK(config)

// 注册回调
sdk.registerCallback(object : PacketCallback {
    override fun onPacketReceived(packet: PacketData, context: ProtocolContext) {
        println("收到数据包: ${packet.metadata}")
    }
    
    override fun onError(exception: CaptureException, context: ProtocolContext) {
        println("处理错误: ${exception.message}")
    }
})

// 启动SDK
sdk.start()

// 停止SDK
sdk.stop()
```

## 项目结构

```
src/main/kotlin/com/company/sdk/
├── core/                   # 核心引擎
├── api/                    # 公共接口
├── model/                  # 数据模型
├── protocol/               # 协议处理
│   ├── http/              # HTTP/HTTPS
│   ├── websocket/         # WebSocket/WSS
│   └── mqtt/              # MQTT/MQTTS
├── security/               # 安全模块
├── callback/               # 回调管理
├── mock/                   # Mock引擎
├── storage/                # 存储扩展
├── fallback/               # 降级策略
└── exception/              # 异常类
```

## 构建和测试

```bash
# 构建项目
./gradlew build

# 运行测试
./gradlew test

# 生成文档
./gradlew dokkaHtml

# 发布到本地仓库
./gradlew publishToMavenLocal
```

## 许可证

Copyright (c) 2024 Company Name. All rights reserved.
