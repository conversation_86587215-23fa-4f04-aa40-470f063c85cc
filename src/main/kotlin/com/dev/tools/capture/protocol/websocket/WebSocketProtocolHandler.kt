package com.dev.tools.capture.protocol.websocket

import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import com.dev.tools.capture.protocol.AbstractProtocolHandler
import com.dev.tools.capture.protocol.ProtocolDetectionResult
import java.nio.charset.StandardCharsets
import java.security.MessageDigest
import java.util.*

/**
 * WebSocket协议处理器
 * 
 * 处理WebSocket和WSS协议的数据包解析和处理
 */
class WebSocketProtocolHandler : AbstractProtocolHandler() {
    
    override val supportedProtocols: Set<Protocol> = setOf(Protocol.WEBSOCKET, Protocol.WSS)
    override val name: String = "WebSocket/WSS Handler"
    
    // WebSocket魔法字符串
    private val webSocketMagicString = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"
    
    override fun detectProtocol(data: ByteArray): ProtocolDetectionResult {
        if (data.isEmpty()) {
            return ProtocolDetectionResult.notDetected("数据为空")
        }
        
        val dataString = data.toUtf8String()
        
        // 检查WebSocket握手请求
        val handshakeResult = detectWebSocketHandshake(dataString)
        if (handshakeResult.isDetected) {
            return handshakeResult
        }
        
        // 检查WebSocket帧
        val frameResult = detectWebSocketFrame(data)
        if (frameResult.isDetected) {
            return frameResult
        }
        
        return ProtocolDetectionResult.notDetected("不是WebSocket协议")
    }
    
    /**
     * 检测WebSocket握手
     */
    private fun detectWebSocketHandshake(data: String): ProtocolDetectionResult {
        val lines = data.split("\r\n", "\n")
        if (lines.isEmpty()) {
            return ProtocolDetectionResult.notDetected("无请求行")
        }
        
        val requestLine = lines[0]
        
        // 检查是否为HTTP升级请求
        if (!requestLine.contains("HTTP/1.1")) {
            return ProtocolDetectionResult.notDetected("不是HTTP/1.1请求")
        }
        
        val headers = parseHeaders(lines)
        
        // 检查必需的WebSocket头部
        val upgrade = headers["upgrade"]?.lowercase()
        val connection = headers["connection"]?.lowercase()
        val webSocketKey = headers["sec-websocket-key"]
        val webSocketVersion = headers["sec-websocket-version"]
        
        if (upgrade != "websocket") {
            return ProtocolDetectionResult.notDetected("Upgrade头部不是websocket")
        }
        
        if (connection?.contains("upgrade") != true) {
            return ProtocolDetectionResult.notDetected("Connection头部不包含upgrade")
        }
        
        if (webSocketKey.isNullOrBlank()) {
            return ProtocolDetectionResult.notDetected("缺少Sec-WebSocket-Key头部")
        }
        
        if (webSocketVersion != "13") {
            return ProtocolDetectionResult.possible(
                Protocol.WEBSOCKET,
                0.7,
                "WebSocket版本不是13: $webSocketVersion"
            )
        }
        
        // 检查是否为WSS（通过端口或其他指示）
        val host = headers["host"]
        val isSecure = host?.contains(":443") == true || 
                      data.contains("wss://", ignoreCase = true)
        
        val protocol = if (isSecure) Protocol.WSS else Protocol.WEBSOCKET
        
        return ProtocolDetectionResult.detected(
            protocol,
            1.0,
            "WebSocket握手请求"
        )
    }
    
    /**
     * 检测WebSocket帧
     */
    private fun detectWebSocketFrame(data: ByteArray): ProtocolDetectionResult {
        if (data.size < 2) {
            return ProtocolDetectionResult.notDetected("数据太短，不是WebSocket帧")
        }
        
        val firstByte = data[0].toInt() and 0xFF
        val secondByte = data[1].toInt() and 0xFF
        
        // 检查FIN位和保留位
        val fin = (firstByte and 0x80) != 0
        val rsv = (firstByte and 0x70) != 0
        val opcode = firstByte and 0x0F
        
        // 检查MASK位和载荷长度
        val mask = (secondByte and 0x80) != 0
        val payloadLength = secondByte and 0x7F
        
        // 验证操作码
        val validOpcodes = setOf(0x0, 0x1, 0x2, 0x8, 0x9, 0xA) // 继续、文本、二进制、关闭、ping、pong
        if (opcode !in validOpcodes) {
            return ProtocolDetectionResult.notDetected("无效的WebSocket操作码: $opcode")
        }
        
        // 保留位应该为0
        if (rsv) {
            return ProtocolDetectionResult.possible(
                Protocol.WEBSOCKET,
                0.6,
                "WebSocket帧但保留位不为0"
            )
        }
        
        // 验证帧长度
        val expectedMinLength = when {
            payloadLength < 126 -> 2 + (if (mask) 4 else 0)
            payloadLength == 126 -> 4 + (if (mask) 4 else 0)
            payloadLength == 127 -> 10 + (if (mask) 4 else 0)
            else -> 2
        }
        
        if (data.size < expectedMinLength) {
            return ProtocolDetectionResult.possible(
                Protocol.WEBSOCKET,
                0.5,
                "WebSocket帧但长度不足"
            )
        }
        
        return ProtocolDetectionResult.detected(
            Protocol.WEBSOCKET,
            0.9,
            "WebSocket帧，操作码: $opcode"
        )
    }
    
    override fun parseProtocolData(rawData: ByteArray, context: ProtocolContext): String? {
        val dataString = rawData.toUtf8String()
        
        return try {
            if (isWebSocketHandshake(dataString)) {
                parseWebSocketHandshake(dataString)
            } else if (isWebSocketFrame(rawData)) {
                parseWebSocketFrame(rawData)
            } else {
                null
            }
        } catch (e: Exception) {
            logger.warn("解析WebSocket数据失败: ${e.message}")
            null
        }
    }
    
    /**
     * 判断是否为WebSocket握手
     */
    private fun isWebSocketHandshake(data: String): Boolean {
        return data.contains("Upgrade: websocket", ignoreCase = true) ||
               data.contains("Sec-WebSocket-Key:", ignoreCase = true)
    }
    
    /**
     * 判断是否为WebSocket帧
     */
    private fun isWebSocketFrame(data: ByteArray): Boolean {
        if (data.size < 2) return false
        
        val firstByte = data[0].toInt() and 0xFF
        val opcode = firstByte and 0x0F
        val validOpcodes = setOf(0x0, 0x1, 0x2, 0x8, 0x9, 0xA)
        
        return opcode in validOpcodes
    }
    
    /**
     * 解析WebSocket握手
     */
    private fun parseWebSocketHandshake(data: String): String {
        val lines = data.split("\r\n", "\n")
        val requestLine = lines[0]
        val headers = parseHeaders(lines)
        
        return buildString {
            if (requestLine.startsWith("HTTP/")) {
                appendLine("WebSocket握手响应:")
                val parts = requestLine.split(" ", limit = 3)
                appendLine("  状态: ${parts.getOrNull(1)} ${parts.getOrNull(2)}")
            } else {
                appendLine("WebSocket握手请求:")
                val parts = requestLine.split(" ", limit = 3)
                appendLine("  方法: ${parts.getOrNull(0)}")
                appendLine("  路径: ${parts.getOrNull(1)}")
                appendLine("  版本: ${parts.getOrNull(2)}")
            }
            
            appendLine("  头部:")
            headers.forEach { (name, value) ->
                appendLine("    $name: $value")
            }
            
            // 如果是握手响应，验证Sec-WebSocket-Accept
            val webSocketKey = headers["sec-websocket-key"]
            val webSocketAccept = headers["sec-websocket-accept"]
            if (webSocketKey != null && webSocketAccept != null) {
                val expectedAccept = generateWebSocketAccept(webSocketKey)
                val isValid = expectedAccept == webSocketAccept
                appendLine("  握手验证: ${if (isValid) "成功" else "失败"}")
            }
        }
    }
    
    /**
     * 解析WebSocket帧
     */
    private fun parseWebSocketFrame(data: ByteArray): String {
        if (data.size < 2) {
            return "WebSocket帧: 数据不完整"
        }
        
        val firstByte = data[0].toInt() and 0xFF
        val secondByte = data[1].toInt() and 0xFF
        
        val fin = (firstByte and 0x80) != 0
        val rsv1 = (firstByte and 0x40) != 0
        val rsv2 = (firstByte and 0x20) != 0
        val rsv3 = (firstByte and 0x10) != 0
        val opcode = firstByte and 0x0F
        
        val mask = (secondByte and 0x80) != 0
        var payloadLength = (secondByte and 0x7F).toLong()
        
        var offset = 2
        
        // 扩展载荷长度
        when (payloadLength) {
            126L -> {
                if (data.size < offset + 2) return "WebSocket帧: 扩展长度数据不完整"
                payloadLength = (((data[offset].toInt() and 0xFF) shl 8) or
                               (data[offset + 1].toInt() and 0xFF)).toLong()
                offset += 2
            }
            127L -> {
                if (data.size < offset + 8) return "WebSocket帧: 扩展长度数据不完整"
                payloadLength = 0
                for (i in 0..7) {
                    payloadLength = (payloadLength shl 8) or (data[offset + i].toLong() and 0xFF)
                }
                offset += 8
            }
        }
        
        // 掩码密钥
        val maskingKey = if (mask) {
            if (data.size < offset + 4) return "WebSocket帧: 掩码密钥数据不完整"
            val key = ByteArray(4)
            System.arraycopy(data, offset, key, 0, 4)
            offset += 4
            key
        } else {
            null
        }
        
        // 载荷数据
        val payload = if (data.size >= offset + payloadLength.toInt()) {
            val payloadData = ByteArray(payloadLength.toInt())
            System.arraycopy(data, offset, payloadData, 0, payloadLength.toInt())
            
            // 如果有掩码，解码数据
            if (maskingKey != null) {
                for (i in payloadData.indices) {
                    payloadData[i] = (payloadData[i].toInt() xor maskingKey[i % 4].toInt()).toByte()
                }
            }
            
            payloadData
        } else {
            ByteArray(0)
        }
        
        val opcodeDescription = when (opcode) {
            0x0 -> "继续帧"
            0x1 -> "文本帧"
            0x2 -> "二进制帧"
            0x8 -> "关闭帧"
            0x9 -> "Ping帧"
            0xA -> "Pong帧"
            else -> "未知帧($opcode)"
        }
        
        return buildString {
            appendLine("WebSocket帧:")
            appendLine("  FIN: $fin")
            appendLine("  RSV: $rsv1,$rsv2,$rsv3")
            appendLine("  操作码: $opcodeDescription")
            appendLine("  掩码: $mask")
            appendLine("  载荷长度: $payloadLength")
            
            if (payload.isNotEmpty()) {
                when (opcode) {
                    0x1 -> { // 文本帧
                        val text = String(payload, StandardCharsets.UTF_8)
                        appendLine("  文本内容: ${text.take(200)}${if (text.length > 200) "..." else ""}")
                    }
                    0x2 -> { // 二进制帧
                        appendLine("  二进制数据: ${payload.size}字节")
                    }
                    0x8 -> { // 关闭帧
                        if (payload.size >= 2) {
                            val closeCode = ((payload[0].toInt() and 0xFF) shl 8) or
                                          (payload[1].toInt() and 0xFF)
                            val reason = if (payload.size > 2) {
                                String(payload, 2, payload.size - 2, StandardCharsets.UTF_8)
                            } else ""
                            appendLine("  关闭码: $closeCode")
                            if (reason.isNotEmpty()) {
                                appendLine("  关闭原因: $reason")
                            }
                        }
                    }
                    0x9, 0xA -> { // Ping/Pong帧
                        if (payload.isNotEmpty()) {
                            val text = String(payload, StandardCharsets.UTF_8)
                            appendLine("  数据: $text")
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 解析HTTP头部
     */
    private fun parseHeaders(lines: List<String>): Map<String, String> {
        val headers = mutableMapOf<String, String>()
        
        for (i in 1 until lines.size) {
            val line = lines[i].trim()
            if (line.isEmpty()) break
            
            val colonIndex = line.indexOf(':')
            if (colonIndex > 0) {
                val name = line.substring(0, colonIndex).trim().lowercase()
                val value = line.substring(colonIndex + 1).trim()
                headers[name] = value
            }
        }
        
        return headers
    }
    
    /**
     * 生成WebSocket Accept值
     */
    private fun generateWebSocketAccept(key: String): String {
        val combined = key + webSocketMagicString
        val digest = MessageDigest.getInstance("SHA-1")
        val hash = digest.digest(combined.toByteArray(StandardCharsets.UTF_8))
        return Base64.getEncoder().encodeToString(hash)
    }
    
    override fun determineDirection(rawData: ByteArray, context: ProtocolContext): PacketData.PacketDirection {
        val dataString = rawData.toUtf8String()
        
        return when {
            dataString.startsWith("GET ") && isWebSocketHandshake(dataString) -> 
                PacketData.PacketDirection.CLIENT_TO_SERVER
            dataString.startsWith("HTTP/") && isWebSocketHandshake(dataString) -> 
                PacketData.PacketDirection.SERVER_TO_CLIENT
            isWebSocketFrame(rawData) -> {
                // WebSocket帧的方向需要根据上下文判断，这里返回未知
                PacketData.PacketDirection.UNKNOWN
            }
            else -> PacketData.PacketDirection.UNKNOWN
        }
    }
    
    override fun extractMetadata(rawData: ByteArray, context: ProtocolContext): Map<String, String> {
        val baseMetadata = super.extractMetadata(rawData, context).toMutableMap()
        
        try {
            val dataString = rawData.toUtf8String()
            
            if (isWebSocketHandshake(dataString)) {
                val headers = parseHeaders(dataString.split("\r\n", "\n"))
                baseMetadata["websocket_type"] = "handshake"
                baseMetadata["websocket_key"] = headers["sec-websocket-key"] ?: ""
                baseMetadata["websocket_version"] = headers["sec-websocket-version"] ?: ""
                baseMetadata["websocket_protocol"] = headers["sec-websocket-protocol"] ?: ""
                baseMetadata["websocket_extensions"] = headers["sec-websocket-extensions"] ?: ""
            } else if (isWebSocketFrame(rawData)) {
                val firstByte = rawData[0].toInt() and 0xFF
                val opcode = firstByte and 0x0F
                val fin = (firstByte and 0x80) != 0
                
                baseMetadata["websocket_type"] = "frame"
                baseMetadata["websocket_opcode"] = opcode.toString()
                baseMetadata["websocket_fin"] = fin.toString()
                
                val opcodeDescription = when (opcode) {
                    0x0 -> "continuation"
                    0x1 -> "text"
                    0x2 -> "binary"
                    0x8 -> "close"
                    0x9 -> "ping"
                    0xA -> "pong"
                    else -> "unknown"
                }
                baseMetadata["websocket_frame_type"] = opcodeDescription
            }
            
        } catch (e: Exception) {
            logger.debug("提取WebSocket元数据失败: ${e.message}")
        }
        
        return baseMetadata
    }
}
