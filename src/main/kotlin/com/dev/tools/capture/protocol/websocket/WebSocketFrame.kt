package com.dev.tools.capture.protocol.websocket

import kotlinx.serialization.Serializable
import java.nio.charset.StandardCharsets

/**
 * WebSocket帧数据类
 */
@Serializable
data class WebSocketFrame(
    /**
     * FIN位 - 表示这是消息的最后一个片段
     */
    val fin: Boolean,
    
    /**
     * RSV1位 - 保留位1
     */
    val rsv1: Boolean = false,
    
    /**
     * RSV2位 - 保留位2
     */
    val rsv2: Boolean = false,
    
    /**
     * RSV3位 - 保留位3
     */
    val rsv3: Boolean = false,
    
    /**
     * 操作码
     */
    val opcode: WebSocketOpcode,
    
    /**
     * 是否使用掩码
     */
    val masked: Boolean,
    
    /**
     * 掩码密钥（如果使用掩码）
     */
    val maskingKey: ByteArray? = null,
    
    /**
     * 载荷数据
     */
    val payload: ByteArray
) {
    
    /**
     * 载荷长度
     */
    val payloadLength: Long
        get() = payload.size.toLong()
    
    /**
     * 是否为控制帧
     */
    val isControlFrame: Boolean
        get() = opcode.isControlFrame
    
    /**
     * 获取文本载荷（仅适用于文本帧）
     */
    val textPayload: String?
        get() = if (opcode == WebSocketOpcode.TEXT) {
            String(payload, StandardCharsets.UTF_8)
        } else null
    
    /**
     * 获取关闭码（仅适用于关闭帧）
     */
    val closeCode: Int?
        get() = if (opcode == WebSocketOpcode.CLOSE && payload.size >= 2) {
            ((payload[0].toInt() and 0xFF) shl 8) or (payload[1].toInt() and 0xFF)
        } else null
    
    /**
     * 获取关闭原因（仅适用于关闭帧）
     */
    val closeReason: String?
        get() = if (opcode == WebSocketOpcode.CLOSE && payload.size > 2) {
            String(payload, 2, payload.size - 2, StandardCharsets.UTF_8)
        } else null
    
    /**
     * 将帧编码为字节数组
     */
    fun encode(): ByteArray {
        val result = mutableListOf<Byte>()
        
        // 第一个字节：FIN + RSV + Opcode
        var firstByte = opcode.value
        if (fin) firstByte = firstByte or 0x80
        if (rsv1) firstByte = firstByte or 0x40
        if (rsv2) firstByte = firstByte or 0x20
        if (rsv3) firstByte = firstByte or 0x10
        result.add(firstByte.toByte())
        
        // 第二个字节：MASK + Payload length
        var secondByte = 0
        if (masked) secondByte = secondByte or 0x80
        
        when {
            payloadLength < 126 -> {
                secondByte = secondByte or payloadLength.toInt()
                result.add(secondByte.toByte())
            }
            payloadLength < 65536 -> {
                secondByte = secondByte or 126
                result.add(secondByte.toByte())
                result.add((payloadLength shr 8).toByte())
                result.add(payloadLength.toByte())
            }
            else -> {
                secondByte = secondByte or 127
                result.add(secondByte.toByte())
                for (i in 7 downTo 0) {
                    result.add((payloadLength shr (i * 8)).toByte())
                }
            }
        }
        
        // 掩码密钥
        if (masked && maskingKey != null) {
            result.addAll(maskingKey.toList())
        }
        
        // 载荷数据
        if (masked && maskingKey != null) {
            // 应用掩码
            for (i in payload.indices) {
                val maskedByte = payload[i].toInt() xor maskingKey[i % 4].toInt()
                result.add(maskedByte.toByte())
            }
        } else {
            result.addAll(payload.toList())
        }
        
        return result.toByteArray()
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as WebSocketFrame
        
        if (fin != other.fin) return false
        if (rsv1 != other.rsv1) return false
        if (rsv2 != other.rsv2) return false
        if (rsv3 != other.rsv3) return false
        if (opcode != other.opcode) return false
        if (masked != other.masked) return false
        if (maskingKey != null) {
            if (other.maskingKey == null) return false
            if (!maskingKey.contentEquals(other.maskingKey)) return false
        } else if (other.maskingKey != null) return false
        if (!payload.contentEquals(other.payload)) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = fin.hashCode()
        result = 31 * result + rsv1.hashCode()
        result = 31 * result + rsv2.hashCode()
        result = 31 * result + rsv3.hashCode()
        result = 31 * result + opcode.hashCode()
        result = 31 * result + masked.hashCode()
        result = 31 * result + (maskingKey?.contentHashCode() ?: 0)
        result = 31 * result + payload.contentHashCode()
        return result
    }
    
    companion object {
        /**
         * 从字节数组解码WebSocket帧
         */
        fun decode(data: ByteArray): WebSocketFrame? {
            if (data.size < 2) return null
            
            val firstByte = data[0].toInt() and 0xFF
            val secondByte = data[1].toInt() and 0xFF
            
            val fin = (firstByte and 0x80) != 0
            val rsv1 = (firstByte and 0x40) != 0
            val rsv2 = (firstByte and 0x20) != 0
            val rsv3 = (firstByte and 0x10) != 0
            val opcodeValue = firstByte and 0x0F
            
            val opcode = WebSocketOpcode.fromValue(opcodeValue) ?: return null
            
            val masked = (secondByte and 0x80) != 0
            var payloadLength = (secondByte and 0x7F).toLong()
            
            var offset = 2
            
            // 扩展载荷长度
            when (payloadLength) {
                126L -> {
                    if (data.size < offset + 2) return null
                    payloadLength = (((data[offset].toInt() and 0xFF) shl 8) or
                                   (data[offset + 1].toInt() and 0xFF)).toLong()
                    offset += 2
                }
                127L -> {
                    if (data.size < offset + 8) return null
                    payloadLength = 0
                    for (i in 0..7) {
                        payloadLength = (payloadLength shl 8) or (data[offset + i].toLong() and 0xFF)
                    }
                    offset += 8
                }
            }
            
            // 掩码密钥
            val maskingKey = if (masked) {
                if (data.size < offset + 4) return null
                val key = ByteArray(4)
                System.arraycopy(data, offset, key, 0, 4)
                offset += 4
                key
            } else null
            
            // 载荷数据
            if (data.size < offset + payloadLength.toInt()) return null
            
            val payload = ByteArray(payloadLength.toInt())
            System.arraycopy(data, offset, payload, 0, payloadLength.toInt())
            
            // 如果有掩码，解码数据
            if (maskingKey != null) {
                for (i in payload.indices) {
                    payload[i] = (payload[i].toInt() xor maskingKey[i % 4].toInt()).toByte()
                }
            }
            
            return WebSocketFrame(
                fin = fin,
                rsv1 = rsv1,
                rsv2 = rsv2,
                rsv3 = rsv3,
                opcode = opcode,
                masked = masked,
                maskingKey = maskingKey,
                payload = payload
            )
        }
        
        /**
         * 创建文本帧
         */
        fun text(text: String, fin: Boolean = true, masked: Boolean = false): WebSocketFrame {
            return WebSocketFrame(
                fin = fin,
                opcode = WebSocketOpcode.TEXT,
                masked = masked,
                maskingKey = if (masked) generateMaskingKey() else null,
                payload = text.toByteArray(StandardCharsets.UTF_8)
            )
        }
        
        /**
         * 创建二进制帧
         */
        fun binary(data: ByteArray, fin: Boolean = true, masked: Boolean = false): WebSocketFrame {
            return WebSocketFrame(
                fin = fin,
                opcode = WebSocketOpcode.BINARY,
                masked = masked,
                maskingKey = if (masked) generateMaskingKey() else null,
                payload = data
            )
        }
        
        /**
         * 创建关闭帧
         */
        fun close(code: Int = 1000, reason: String = "", masked: Boolean = false): WebSocketFrame {
            val payload = ByteArray(2 + reason.toByteArray(StandardCharsets.UTF_8).size)
            payload[0] = (code shr 8).toByte()
            payload[1] = code.toByte()
            
            val reasonBytes = reason.toByteArray(StandardCharsets.UTF_8)
            System.arraycopy(reasonBytes, 0, payload, 2, reasonBytes.size)
            
            return WebSocketFrame(
                fin = true,
                opcode = WebSocketOpcode.CLOSE,
                masked = masked,
                maskingKey = if (masked) generateMaskingKey() else null,
                payload = payload
            )
        }
        
        /**
         * 创建Ping帧
         */
        fun ping(data: ByteArray = ByteArray(0), masked: Boolean = false): WebSocketFrame {
            return WebSocketFrame(
                fin = true,
                opcode = WebSocketOpcode.PING,
                masked = masked,
                maskingKey = if (masked) generateMaskingKey() else null,
                payload = data
            )
        }
        
        /**
         * 创建Pong帧
         */
        fun pong(data: ByteArray = ByteArray(0), masked: Boolean = false): WebSocketFrame {
            return WebSocketFrame(
                fin = true,
                opcode = WebSocketOpcode.PONG,
                masked = masked,
                maskingKey = if (masked) generateMaskingKey() else null,
                payload = data
            )
        }
        
        /**
         * 生成随机掩码密钥
         */
        private fun generateMaskingKey(): ByteArray {
            val key = ByteArray(4)
            java.security.SecureRandom().nextBytes(key)
            return key
        }
    }
}
