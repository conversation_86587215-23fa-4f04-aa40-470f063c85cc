package com.dev.tools.capture.protocol

import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import io.netty.buffer.ByteBuf
import io.netty.channel.ChannelHandlerContext

/**
 * 协议处理器接口
 * 
 * 定义了处理特定网络协议的标准接口
 */
interface ProtocolHandler {
    
    /**
     * 获取支持的协议类型
     */
    val supportedProtocols: Set<Protocol>
    
    /**
     * 获取处理器名称
     */
    val name: String
    
    /**
     * 检查是否可以处理指定的协议
     * 
     * @param protocol 协议类型
     * @return 是否支持
     */
    fun canHandle(protocol: Protocol): Boolean = protocol in supportedProtocols
    
    /**
     * 检测数据是否符合此协议
     * 
     * @param data 原始数据
     * @return 检测结果
     */
    fun detectProtocol(data: ByteArray): ProtocolDetectionResult
    
    /**
     * 解析数据包
     * 
     * @param rawData 原始数据
     * @param context 协议上下文
     * @return 解析后的数据包
     */
    fun parsePacket(rawData: ByteArray, context: ProtocolContext): PacketData
    
    /**
     * 处理入站数据（客户端到服务器）
     * 
     * @param ctx Netty通道上下文
     * @param data 数据缓冲区
     * @param context 协议上下文
     * @return 处理结果
     */
    fun handleInbound(
        ctx: ChannelHandlerContext,
        data: ByteBuf,
        context: ProtocolContext
    ): ProtocolHandleResult
    
    /**
     * 处理出站数据（服务器到客户端）
     * 
     * @param ctx Netty通道上下文
     * @param data 数据缓冲区
     * @param context 协议上下文
     * @return 处理结果
     */
    fun handleOutbound(
        ctx: ChannelHandlerContext,
        data: ByteBuf,
        context: ProtocolContext
    ): ProtocolHandleResult
    
    /**
     * 建立连接时的初始化处理
     * 
     * @param ctx Netty通道上下文
     * @param context 协议上下文
     */
    fun onConnectionEstablished(ctx: ChannelHandlerContext, context: ProtocolContext) {
        // 默认空实现
    }
    
    /**
     * 连接关闭时的清理处理
     * 
     * @param ctx Netty通道上下文
     * @param context 协议上下文
     */
    fun onConnectionClosed(ctx: ChannelHandlerContext, context: ProtocolContext) {
        // 默认空实现
    }
    
    /**
     * 处理异常
     * 
     * @param ctx Netty通道上下文
     * @param cause 异常原因
     * @param context 协议上下文
     */
    fun onException(ctx: ChannelHandlerContext, cause: Throwable, context: ProtocolContext) {
        // 默认空实现
    }
}

/**
 * 协议检测结果
 */
data class ProtocolDetectionResult(
    val protocol: Protocol?,
    val confidence: Double, // 0.0 - 1.0
    val reason: String = ""
) {
    /**
     * 是否检测成功
     */
    val isDetected: Boolean
        get() = protocol != null && confidence > 0.5
    
    companion object {
        /**
         * 创建成功检测结果
         */
        fun detected(protocol: Protocol, confidence: Double = 1.0, reason: String = ""): ProtocolDetectionResult {
            return ProtocolDetectionResult(protocol, confidence, reason)
        }
        
        /**
         * 创建未检测到结果
         */
        fun notDetected(reason: String = "协议不匹配"): ProtocolDetectionResult {
            return ProtocolDetectionResult(null, 0.0, reason)
        }
        
        /**
         * 创建可能匹配结果
         */
        fun possible(protocol: Protocol, confidence: Double, reason: String = ""): ProtocolDetectionResult {
            return ProtocolDetectionResult(protocol, confidence, reason)
        }
    }
}

/**
 * 协议处理结果
 */
data class ProtocolHandleResult(
    val action: HandleAction,
    val modifiedData: ByteArray? = null,
    val additionalData: List<ByteArray> = emptyList(),
    val metadata: Map<String, Any> = emptyMap()
) {
    /**
     * 处理动作枚举
     */
    enum class HandleAction {
        /** 继续传递原始数据 */
        PASS_THROUGH,
        /** 传递修改后的数据 */
        PASS_MODIFIED,
        /** 阻止数据传递 */
        BLOCK,
        /** 发送额外数据 */
        SEND_ADDITIONAL,
        /** 关闭连接 */
        CLOSE_CONNECTION
    }
    
    companion object {
        /**
         * 直接传递
         */
        fun passThrough(metadata: Map<String, Any> = emptyMap()): ProtocolHandleResult {
            return ProtocolHandleResult(HandleAction.PASS_THROUGH, metadata = metadata)
        }
        
        /**
         * 传递修改后的数据
         */
        fun passModified(
            modifiedData: ByteArray,
            metadata: Map<String, Any> = emptyMap()
        ): ProtocolHandleResult {
            return ProtocolHandleResult(HandleAction.PASS_MODIFIED, modifiedData, metadata = metadata)
        }
        
        /**
         * 阻止传递
         */
        fun block(reason: String = ""): ProtocolHandleResult {
            return ProtocolHandleResult(
                HandleAction.BLOCK,
                metadata = if (reason.isNotEmpty()) mapOf("reason" to reason) else emptyMap()
            )
        }
        
        /**
         * 发送额外数据
         */
        fun sendAdditional(
            additionalData: List<ByteArray>,
            metadata: Map<String, Any> = emptyMap()
        ): ProtocolHandleResult {
            return ProtocolHandleResult(HandleAction.SEND_ADDITIONAL, additionalData = additionalData, metadata = metadata)
        }
        
        /**
         * 关闭连接
         */
        fun closeConnection(reason: String = ""): ProtocolHandleResult {
            return ProtocolHandleResult(
                HandleAction.CLOSE_CONNECTION,
                metadata = if (reason.isNotEmpty()) mapOf("reason" to reason) else emptyMap()
            )
        }
    }
}
