package com.dev.tools.capture.protocol

import com.dev.tools.capture.exception.ProtocolParseException
import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import io.netty.buffer.ByteBuf
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory

/**
 * 抽象协议处理器基类
 * 
 * 提供协议处理器的通用功能和默认实现
 */
abstract class AbstractProtocolHandler : ProtocolHandler {
    
    protected val logger = LoggerFactory.getLogger(this::class.java)
    
    /**
     * 从ByteBuf中读取字节数组
     */
    protected fun ByteBuf.toByteArray(): ByteArray {
        val bytes = ByteArray(readableBytes())
        getBytes(readerIndex(), bytes)
        return bytes
    }
    
    /**
     * 安全地从ByteBuf中读取字节数组（不改变读取位置）
     */
    protected fun ByteBuf.peekByteArray(length: Int = readableBytes()): ByteArray {
        val actualLength = minOf(length, readableBytes())
        val bytes = ByteArray(actualLength)
        getBytes(readerIndex(), bytes, 0, actualLength)
        return bytes
    }
    
    /**
     * 检查数据是否以指定字节序列开头
     */
    protected fun ByteArray.startsWith(prefix: ByteArray): Boolean {
        if (size < prefix.size) return false
        return prefix.indices.all { this[it] == prefix[it] }
    }
    
    /**
     * 检查数据是否包含指定字节序列
     */
    protected fun ByteArray.contains(sequence: ByteArray): Boolean {
        if (size < sequence.size) return false
        for (i in 0..size - sequence.size) {
            if (sequence.indices.all { this[i + it] == sequence[it] }) {
                return true
            }
        }
        return false
    }
    
    /**
     * 将字节数组转换为字符串（UTF-8）
     */
    protected fun ByteArray.toUtf8String(): String {
        return String(this, Charsets.UTF_8)
    }
    
    /**
     * 查找字节序列的位置
     */
    protected fun ByteArray.indexOf(sequence: ByteArray, startIndex: Int = 0): Int {
        if (size < sequence.size || startIndex < 0) return -1
        for (i in startIndex..size - sequence.size) {
            if (sequence.indices.all { this[i + it] == sequence[it] }) {
                return i
            }
        }
        return -1
    }
    
    /**
     * 默认的数据包解析实现
     */
    override fun parsePacket(rawData: ByteArray, context: ProtocolContext): PacketData {
        try {
            val parsedData = parseProtocolData(rawData, context)
            
            return PacketData(
                rawData = rawData,
                parsedData = parsedData,
                metadata = extractMetadata(rawData, context),
                direction = determineDirection(rawData, context)
            )
            
        } catch (e: Exception) {
            logger.warn("解析协议数据失败 [${context.protocol}]: ${e.message}")
            throw ProtocolParseException.invalidFormat(
                protocol = context.protocol,
                reason = e.message ?: "未知错误",
                context = context,
                rawData = rawData
            )
        }
    }
    
    /**
     * 解析协议特定数据（子类实现）
     * 
     * @param rawData 原始数据
     * @param context 协议上下文
     * @return 解析后的数据字符串
     */
    protected abstract fun parseProtocolData(rawData: ByteArray, context: ProtocolContext): String?
    
    /**
     * 提取元数据（子类可重写）
     * 
     * @param rawData 原始数据
     * @param context 协议上下文
     * @return 元数据映射
     */
    protected open fun extractMetadata(rawData: ByteArray, context: ProtocolContext): Map<String, String> {
        return mapOf(
            "protocol" to context.protocol.name,
            "size" to rawData.size.toString(),
            "timestamp" to System.currentTimeMillis().toString()
        )
    }
    
    /**
     * 确定数据包方向（子类可重写）
     * 
     * @param rawData 原始数据
     * @param context 协议上下文
     * @return 数据包方向
     */
    protected open fun determineDirection(rawData: ByteArray, context: ProtocolContext): PacketData.PacketDirection {
        // 默认返回未知方向，子类应该根据协议特性实现
        return PacketData.PacketDirection.UNKNOWN
    }
    
    /**
     * 默认的入站处理实现
     */
    override fun handleInbound(
        ctx: ChannelHandlerContext,
        data: ByteBuf,
        context: ProtocolContext
    ): ProtocolHandleResult {
        return try {
            val rawData = data.toByteArray()
            val packet = parsePacket(rawData, context.copy(
                metadata = context.metadata + ("direction" to "inbound")
            ))
            
            // 记录数据包
            logger.debug("处理入站数据包 [${context.protocol}]: ${packet.size}字节")
            
            // 默认直接传递
            ProtocolHandleResult.passThrough(
                metadata = mapOf(
                    "packet_id" to packet.id,
                    "direction" to "inbound"
                )
            )
            
        } catch (e: Exception) {
            logger.error("处理入站数据失败 [${context.protocol}]: ${e.message}", e)
            ProtocolHandleResult.block("入站数据处理失败: ${e.message}")
        }
    }
    
    /**
     * 默认的出站处理实现
     */
    override fun handleOutbound(
        ctx: ChannelHandlerContext,
        data: ByteBuf,
        context: ProtocolContext
    ): ProtocolHandleResult {
        return try {
            val rawData = data.toByteArray()
            val packet = parsePacket(rawData, context.copy(
                metadata = context.metadata + ("direction" to "outbound")
            ))
            
            // 记录数据包
            logger.debug("处理出站数据包 [${context.protocol}]: ${packet.size}字节")
            
            // 默认直接传递
            ProtocolHandleResult.passThrough(
                metadata = mapOf(
                    "packet_id" to packet.id,
                    "direction" to "outbound"
                )
            )
            
        } catch (e: Exception) {
            logger.error("处理出站数据失败 [${context.protocol}]: ${e.message}", e)
            ProtocolHandleResult.block("出站数据处理失败: ${e.message}")
        }
    }
    
    /**
     * 默认的连接建立处理
     */
    override fun onConnectionEstablished(ctx: ChannelHandlerContext, context: ProtocolContext) {
        logger.debug("连接建立 [${context.protocol}]: ${context.session.source} -> ${context.session.destination}")
    }
    
    /**
     * 默认的连接关闭处理
     */
    override fun onConnectionClosed(ctx: ChannelHandlerContext, context: ProtocolContext) {
        logger.debug("连接关闭 [${context.protocol}]: 会话 ${context.session.id}")
    }
    
    /**
     * 默认的异常处理
     */
    override fun onException(ctx: ChannelHandlerContext, cause: Throwable, context: ProtocolContext) {
        logger.warn("协议处理异常 [${context.protocol}]: ${cause.message}", cause)
    }
    
    /**
     * 工具方法：检查数据是否足够长
     */
    protected fun checkDataLength(data: ByteArray, minLength: Int, protocolName: String) {
        if (data.size < minLength) {
            throw ProtocolParseException.incompleteData(
                protocol = supportedProtocols.first(),
                expectedSize = minLength,
                actualSize = data.size
            )
        }
    }
    
    /**
     * 工具方法：安全地获取字符串
     */
    protected fun ByteArray.safeSubstring(start: Int, end: Int = size): String {
        val actualStart = maxOf(0, start)
        val actualEnd = minOf(size, end)
        if (actualStart >= actualEnd) return ""
        
        return sliceArray(actualStart until actualEnd).toUtf8String()
    }
}
