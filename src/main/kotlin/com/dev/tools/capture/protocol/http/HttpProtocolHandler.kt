package com.dev.tools.capture.protocol.http

import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import com.dev.tools.capture.protocol.AbstractProtocolHandler
import com.dev.tools.capture.protocol.ProtocolDetectionResult

/**
 * HTTP协议处理器
 * 
 * 处理HTTP和HTTPS协议的数据包解析和处理
 */
class HttpProtocolHandler : AbstractProtocolHandler() {
    
    override val supportedProtocols: Set<Protocol> = setOf(Protocol.HTTP, Protocol.HTTPS)
    override val name: String = "HTTP/HTTPS Handler"
    
    // HTTP方法
    private val httpMethods = setOf(
        "GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH", "TRACE", "CONNECT"
    )
    
    // HTTP版本
    private val httpVersions = setOf("HTTP/1.0", "HTTP/1.1", "HTTP/2.0")
    
    override fun detectProtocol(data: ByteArray): ProtocolDetectionResult {
        if (data.isEmpty()) {
            return ProtocolDetectionResult.notDetected("数据为空")
        }
        
        val dataString = data.toUtf8String()
        
        // 检查HTTP请求
        val requestResult = detectHttpRequest(dataString)
        if (requestResult.isDetected) {
            return requestResult
        }
        
        // 检查HTTP响应
        val responseResult = detectHttpResponse(dataString)
        if (responseResult.isDetected) {
            return responseResult
        }
        
        return ProtocolDetectionResult.notDetected("不是HTTP协议")
    }
    
    /**
     * 检测HTTP请求
     */
    private fun detectHttpRequest(data: String): ProtocolDetectionResult {
        val lines = data.split("\r\n", "\n")
        if (lines.isEmpty()) {
            return ProtocolDetectionResult.notDetected("无请求行")
        }
        
        val requestLine = lines[0]
        val parts = requestLine.split(" ")
        
        if (parts.size < 3) {
            return ProtocolDetectionResult.notDetected("请求行格式错误")
        }
        
        val method = parts[0].uppercase()
        val version = parts[2].uppercase()
        
        // 检查HTTP方法
        if (method !in httpMethods) {
            return ProtocolDetectionResult.notDetected("未知HTTP方法: $method")
        }
        
        // 检查HTTP版本
        if (version !in httpVersions) {
            return ProtocolDetectionResult.possible(
                Protocol.HTTP, 
                0.7, 
                "可能的HTTP请求，但版本不标准: $version"
            )
        }
        
        // 检查是否有Host头（HTTP/1.1要求）
        val hasHostHeader = lines.any { line ->
            line.lowercase().startsWith("host:")
        }
        
        val confidence = if (hasHostHeader && version == "HTTP/1.1") 1.0 else 0.9
        
        return ProtocolDetectionResult.detected(
            Protocol.HTTP,
            confidence,
            "HTTP请求: $method $version"
        )
    }
    
    /**
     * 检测HTTP响应
     */
    private fun detectHttpResponse(data: String): ProtocolDetectionResult {
        val lines = data.split("\r\n", "\n")
        if (lines.isEmpty()) {
            return ProtocolDetectionResult.notDetected("无状态行")
        }
        
        val statusLine = lines[0]
        val parts = statusLine.split(" ", limit = 3)
        
        if (parts.size < 2) {
            return ProtocolDetectionResult.notDetected("状态行格式错误")
        }
        
        val version = parts[0].uppercase()
        val statusCode = parts[1]
        
        // 检查HTTP版本
        if (version !in httpVersions) {
            return ProtocolDetectionResult.notDetected("不是HTTP版本: $version")
        }
        
        // 检查状态码
        if (!statusCode.matches(Regex("\\d{3}"))) {
            return ProtocolDetectionResult.notDetected("状态码格式错误: $statusCode")
        }
        
        return ProtocolDetectionResult.detected(
            Protocol.HTTP,
            1.0,
            "HTTP响应: $version $statusCode"
        )
    }
    
    override fun parseProtocolData(rawData: ByteArray, context: ProtocolContext): String? {
        val dataString = rawData.toUtf8String()
        
        return try {
            if (isHttpRequest(dataString)) {
                parseHttpRequest(dataString)
            } else if (isHttpResponse(dataString)) {
                parseHttpResponse(dataString)
            } else {
                null
            }
        } catch (e: Exception) {
            logger.warn("解析HTTP数据失败: ${e.message}")
            null
        }
    }
    
    /**
     * 判断是否为HTTP请求
     */
    private fun isHttpRequest(data: String): Boolean {
        val firstLine = data.split("\r\n", "\n").firstOrNull() ?: return false
        val parts = firstLine.split(" ")
        return parts.size >= 3 && parts[0].uppercase() in httpMethods
    }
    
    /**
     * 判断是否为HTTP响应
     */
    private fun isHttpResponse(data: String): Boolean {
        val firstLine = data.split("\r\n", "\n").firstOrNull() ?: return false
        val parts = firstLine.split(" ")
        return parts.size >= 2 && parts[0].uppercase() in httpVersions
    }
    
    /**
     * 解析HTTP请求
     */
    private fun parseHttpRequest(data: String): String {
        val lines = data.split("\r\n", "\n")
        val requestLine = lines[0]
        val parts = requestLine.split(" ", limit = 3)
        
        val method = parts[0]
        val path = parts.getOrNull(1) ?: "/"
        val version = parts.getOrNull(2) ?: "HTTP/1.1"
        
        val headers = mutableMapOf<String, String>()
        var bodyStart = -1
        
        // 解析头部
        for (i in 1 until lines.size) {
            val line = lines[i]
            if (line.isEmpty()) {
                bodyStart = i + 1
                break
            }
            
            val colonIndex = line.indexOf(':')
            if (colonIndex > 0) {
                val headerName = line.substring(0, colonIndex).trim()
                val headerValue = line.substring(colonIndex + 1).trim()
                headers[headerName] = headerValue
            }
        }
        
        // 获取请求体
        val body = if (bodyStart >= 0 && bodyStart < lines.size) {
            lines.subList(bodyStart, lines.size).joinToString("\n")
        } else {
            ""
        }
        
        return buildString {
            appendLine("HTTP Request:")
            appendLine("  Method: $method")
            appendLine("  Path: $path")
            appendLine("  Version: $version")
            if (headers.isNotEmpty()) {
                appendLine("  Headers:")
                headers.forEach { (name, value) ->
                    appendLine("    $name: $value")
                }
            }
            if (body.isNotEmpty()) {
                appendLine("  Body: ${body.take(200)}${if (body.length > 200) "..." else ""}")
            }
        }
    }
    
    /**
     * 解析HTTP响应
     */
    private fun parseHttpResponse(data: String): String {
        val lines = data.split("\r\n", "\n")
        val statusLine = lines[0]
        val parts = statusLine.split(" ", limit = 3)
        
        val version = parts[0]
        val statusCode = parts.getOrNull(1) ?: "000"
        val reasonPhrase = parts.getOrNull(2) ?: ""
        
        val headers = mutableMapOf<String, String>()
        var bodyStart = -1
        
        // 解析头部
        for (i in 1 until lines.size) {
            val line = lines[i]
            if (line.isEmpty()) {
                bodyStart = i + 1
                break
            }
            
            val colonIndex = line.indexOf(':')
            if (colonIndex > 0) {
                val headerName = line.substring(0, colonIndex).trim()
                val headerValue = line.substring(colonIndex + 1).trim()
                headers[headerName] = headerValue
            }
        }
        
        // 获取响应体
        val body = if (bodyStart >= 0 && bodyStart < lines.size) {
            lines.subList(bodyStart, lines.size).joinToString("\n")
        } else {
            ""
        }
        
        return buildString {
            appendLine("HTTP Response:")
            appendLine("  Version: $version")
            appendLine("  Status: $statusCode $reasonPhrase")
            if (headers.isNotEmpty()) {
                appendLine("  Headers:")
                headers.forEach { (name, value) ->
                    appendLine("    $name: $value")
                }
            }
            if (body.isNotEmpty()) {
                appendLine("  Body: ${body.take(200)}${if (body.length > 200) "..." else ""}")
            }
        }
    }
    
    override fun determineDirection(rawData: ByteArray, context: ProtocolContext): PacketData.PacketDirection {
        val dataString = rawData.toUtf8String()
        
        return when {
            isHttpRequest(dataString) -> PacketData.PacketDirection.CLIENT_TO_SERVER
            isHttpResponse(dataString) -> PacketData.PacketDirection.SERVER_TO_CLIENT
            else -> PacketData.PacketDirection.UNKNOWN
        }
    }
    
    override fun extractMetadata(rawData: ByteArray, context: ProtocolContext): Map<String, String> {
        val baseMetadata = super.extractMetadata(rawData, context).toMutableMap()
        
        try {
            val dataString = rawData.toUtf8String()
            val firstLine = dataString.split("\r\n", "\n").firstOrNull() ?: ""
            
            if (isHttpRequest(dataString)) {
                val parts = firstLine.split(" ", limit = 3)
                baseMetadata["http_method"] = parts.getOrNull(0) ?: ""
                baseMetadata["http_path"] = parts.getOrNull(1) ?: ""
                baseMetadata["http_version"] = parts.getOrNull(2) ?: ""
                baseMetadata["http_type"] = "request"
            } else if (isHttpResponse(dataString)) {
                val parts = firstLine.split(" ", limit = 3)
                baseMetadata["http_version"] = parts.getOrNull(0) ?: ""
                baseMetadata["http_status"] = parts.getOrNull(1) ?: ""
                baseMetadata["http_reason"] = parts.getOrNull(2) ?: ""
                baseMetadata["http_type"] = "response"
            }
            
        } catch (e: Exception) {
            logger.debug("提取HTTP元数据失败: ${e.message}")
        }
        
        return baseMetadata
    }
}
