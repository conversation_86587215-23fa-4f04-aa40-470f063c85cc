package com.dev.tools.capture.protocol

import com.dev.tools.capture.exception.UnsupportedProtocolException
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap

/**
 * 协议管理器
 * 
 * 负责管理所有协议处理器，提供协议检测和处理器选择功能
 */
class ProtocolManager {
    
    private val logger = LoggerFactory.getLogger(ProtocolManager::class.java)
    
    // 协议处理器映射：协议类型 -> 处理器
    private val handlers = ConcurrentHashMap<Protocol, ProtocolHandler>()
    
    // 所有处理器列表（用于协议检测）
    private val allHandlers = mutableListOf<ProtocolHandler>()
    
    /**
     * 注册协议处理器
     * 
     * @param handler 协议处理器
     */
    fun registerHandler(handler: ProtocolHandler) {
        handler.supportedProtocols.forEach { protocol ->
            val existingHandler = handlers.put(protocol, handler)
            if (existingHandler != null) {
                logger.warn("协议处理器被替换 [$protocol]: ${existingHandler.name} -> ${handler.name}")
            } else {
                logger.info("注册协议处理器 [$protocol]: ${handler.name}")
            }
        }
        
        if (!allHandlers.contains(handler)) {
            allHandlers.add(handler)
        }
    }
    
    /**
     * 注销协议处理器
     * 
     * @param handler 协议处理器
     */
    fun unregisterHandler(handler: ProtocolHandler) {
        handler.supportedProtocols.forEach { protocol ->
            val removed = handlers.remove(protocol, handler)
            if (removed) {
                logger.info("注销协议处理器 [$protocol]: ${handler.name}")
            }
        }
        
        allHandlers.remove(handler)
    }
    
    /**
     * 获取协议处理器
     * 
     * @param protocol 协议类型
     * @return 协议处理器
     * @throws UnsupportedProtocolException 如果协议不支持
     */
    @Throws(UnsupportedProtocolException::class)
    fun getHandler(protocol: Protocol): ProtocolHandler {
        return handlers[protocol] 
            ?: throw UnsupportedProtocolException(
                protocol = protocol.name,
                supportedProtocols = handlers.keys
            )
    }
    
    /**
     * 检查是否支持指定协议
     * 
     * @param protocol 协议类型
     * @return 是否支持
     */
    fun isSupported(protocol: Protocol): Boolean {
        return handlers.containsKey(protocol)
    }
    
    /**
     * 获取所有支持的协议
     * 
     * @return 支持的协议集合
     */
    fun getSupportedProtocols(): Set<Protocol> {
        return handlers.keys.toSet()
    }
    
    /**
     * 自动检测协议类型
     * 
     * @param data 原始数据
     * @return 检测结果列表，按置信度降序排列
     */
    fun detectProtocol(data: ByteArray): List<ProtocolDetectionResult> {
        if (data.isEmpty()) {
            return emptyList()
        }
        
        val results = mutableListOf<ProtocolDetectionResult>()
        
        // 让所有处理器尝试检测
        allHandlers.forEach { handler ->
            try {
                val result = handler.detectProtocol(data)
                if (result.confidence > 0.0) {
                    results.add(result)
                }
            } catch (e: Exception) {
                logger.debug("协议检测失败 [${handler.name}]: ${e.message}")
            }
        }
        
        // 按置信度降序排列
        results.sortByDescending { it.confidence }
        
        logger.debug("协议检测结果: ${results.size}个候选")
        results.forEach { result ->
            logger.debug("  ${result.protocol?.name ?: "UNKNOWN"}: ${result.confidence} - ${result.reason}")
        }
        
        return results
    }
    
    /**
     * 获取最佳协议检测结果
     * 
     * @param data 原始数据
     * @return 最佳检测结果，如果没有检测到则返回null
     */
    fun detectBestProtocol(data: ByteArray): ProtocolDetectionResult? {
        val results = detectProtocol(data)
        return results.firstOrNull { it.isDetected }
    }
    
    /**
     * 创建协议上下文
     * 
     * @param protocol 协议类型
     * @param source 来源地址
     * @param destination 目标地址
     * @param sessionAttributes 会话属性
     * @param contextMetadata 上下文元数据
     * @return 协议上下文
     */
    fun createContext(
        protocol: Protocol,
        source: String,
        destination: String,
        sessionAttributes: Map<String, String> = emptyMap(),
        contextMetadata: Map<String, String> = emptyMap()
    ): ProtocolContext {
        return ProtocolContext.create(
            protocol = protocol,
            source = source,
            destination = destination,
            sessionAttributes = sessionAttributes,
            contextMetadata = contextMetadata
        )
    }
    
    /**
     * 获取处理器统计信息
     * 
     * @return 统计信息映射
     */
    fun getHandlerStats(): Map<String, Any> {
        return mapOf(
            "totalHandlers" to allHandlers.size,
            "supportedProtocols" to handlers.keys.map { it.name },
            "handlerDetails" to allHandlers.map { handler ->
                mapOf(
                    "name" to handler.name,
                    "supportedProtocols" to handler.supportedProtocols.map { it.name }
                )
            }
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        handlers.clear()
        allHandlers.clear()
        logger.info("协议管理器已清理")
    }
}
