package com.dev.tools.capture.protocol.mqtt

import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import com.dev.tools.capture.protocol.AbstractProtocolHandler
import com.dev.tools.capture.protocol.ProtocolDetectionResult
import java.nio.charset.StandardCharsets

/**
 * MQTT协议处理器
 * 
 * 处理MQTT和MQTTS协议的数据包解析和处理
 */
class MqttProtocolHandler : AbstractProtocolHandler() {
    
    override val supportedProtocols: Set<Protocol> = setOf(Protocol.MQTT, Protocol.MQTTS)
    override val name: String = "MQTT/MQTTS Handler"
    
    override fun detectProtocol(data: ByteArray): ProtocolDetectionResult {
        if (data.isEmpty()) {
            return ProtocolDetectionResult.notDetected("数据为空")
        }
        
        // 检查MQTT固定头部
        val mqttResult = detectMqttPacket(data)
        if (mqttResult.isDetected) {
            return mqttResult
        }
        
        return ProtocolDetectionResult.notDetected("不是MQTT协议")
    }
    
    /**
     * 检测MQTT数据包
     */
    private fun detectMqttPacket(data: ByteArray): ProtocolDetectionResult {
        if (data.size < 2) {
            return ProtocolDetectionResult.notDetected("数据太短，不是MQTT包")
        }
        
        val firstByte = data[0].toInt() and 0xFF
        
        // 提取消息类型（高4位）
        val messageType = (firstByte shr 4) and 0x0F
        
        // 检查消息类型是否有效
        val validMessageTypes = setOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14)
        if (messageType !in validMessageTypes) {
            return ProtocolDetectionResult.notDetected("无效的MQTT消息类型: $messageType")
        }
        
        // 检查DUP、QoS、RETAIN标志
        val dup = (firstByte and 0x08) != 0
        val qos = (firstByte shr 1) and 0x03
        val retain = (firstByte and 0x01) != 0
        
        // QoS值应该在0-2之间
        if (qos > 2) {
            return ProtocolDetectionResult.notDetected("无效的QoS值: $qos")
        }
        
        // 解析剩余长度
        val remainingLengthResult = parseRemainingLength(data, 1)
        if (remainingLengthResult.first == -1) {
            return ProtocolDetectionResult.notDetected("无效的剩余长度编码")
        }
        
        val remainingLength = remainingLengthResult.first
        val remainingLengthBytes = remainingLengthResult.second
        
        // 检查数据包长度是否匹配
        val expectedTotalLength = 1 + remainingLengthBytes + remainingLength
        if (data.size < expectedTotalLength) {
            return ProtocolDetectionResult.possible(
                Protocol.MQTT,
                0.7,
                "MQTT包但数据不完整"
            )
        }
        
        // 特殊检查CONNECT包
        if (messageType == 1) { // CONNECT
            val connectResult = validateConnectPacket(data, 1 + remainingLengthBytes, remainingLength)
            if (connectResult.isDetected) {
                return connectResult
            }
        }
        
        val messageTypeName = getMqttMessageTypeName(messageType)
        
        return ProtocolDetectionResult.detected(
            Protocol.MQTT,
            0.9,
            "MQTT包: $messageTypeName"
        )
    }
    
    /**
     * 解析MQTT剩余长度字段
     * 
     * @param data 数据
     * @param startIndex 开始索引
     * @return Pair(剩余长度, 剩余长度字段占用的字节数)，如果解析失败返回(-1, 0)
     */
    private fun parseRemainingLength(data: ByteArray, startIndex: Int): Pair<Int, Int> {
        var multiplier = 1
        var value = 0
        var index = startIndex
        var byteCount = 0
        
        do {
            if (index >= data.size) {
                return Pair(-1, 0)
            }
            
            val encodedByte = data[index].toInt() and 0xFF
            value += (encodedByte and 0x7F) * multiplier
            
            if (multiplier > 128 * 128 * 128) {
                return Pair(-1, 0) // 剩余长度字段过长
            }
            
            multiplier *= 128
            index++
            byteCount++
            
            if (byteCount > 4) {
                return Pair(-1, 0) // 剩余长度字段最多4字节
            }
            
        } while ((encodedByte and 0x80) != 0)
        
        return Pair(value, byteCount)
    }
    
    /**
     * 验证CONNECT包
     */
    private fun validateConnectPacket(data: ByteArray, payloadStart: Int, payloadLength: Int): ProtocolDetectionResult {
        if (payloadLength < 10) { // 最小CONNECT包长度
            return ProtocolDetectionResult.notDetected("CONNECT包载荷太短")
        }
        
        var offset = payloadStart
        
        // 读取协议名称长度
        if (offset + 2 > data.size) {
            return ProtocolDetectionResult.notDetected("CONNECT包协议名称长度不完整")
        }
        
        val protocolNameLength = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
        offset += 2
        
        // 读取协议名称
        if (offset + protocolNameLength > data.size) {
            return ProtocolDetectionResult.notDetected("CONNECT包协议名称不完整")
        }
        
        val protocolName = String(data, offset, protocolNameLength, StandardCharsets.UTF_8)
        offset += protocolNameLength
        
        // 检查协议名称
        if (protocolName != "MQTT" && protocolName != "MQIsdp") {
            return ProtocolDetectionResult.notDetected("无效的MQTT协议名称: $protocolName")
        }
        
        // 读取协议版本
        if (offset >= data.size) {
            return ProtocolDetectionResult.notDetected("CONNECT包协议版本不完整")
        }
        
        val protocolVersion = data[offset].toInt() and 0xFF
        offset++
        
        // 检查协议版本
        val validVersions = setOf(3, 4, 5) // MQTT 3.1, 3.1.1, 5.0
        if (protocolVersion !in validVersions) {
            return ProtocolDetectionResult.possible(
                Protocol.MQTT,
                0.6,
                "MQTT CONNECT但版本不标准: $protocolVersion"
            )
        }
        
        return ProtocolDetectionResult.detected(
            Protocol.MQTT,
            1.0,
            "MQTT CONNECT包，协议: $protocolName v$protocolVersion"
        )
    }
    
    /**
     * 获取MQTT消息类型名称
     */
    private fun getMqttMessageTypeName(messageType: Int): String {
        return when (messageType) {
            1 -> "CONNECT"
            2 -> "CONNACK"
            3 -> "PUBLISH"
            4 -> "PUBACK"
            5 -> "PUBREC"
            6 -> "PUBREL"
            7 -> "PUBCOMP"
            8 -> "SUBSCRIBE"
            9 -> "SUBACK"
            10 -> "UNSUBSCRIBE"
            11 -> "UNSUBACK"
            12 -> "PINGREQ"
            13 -> "PINGRESP"
            14 -> "DISCONNECT"
            15 -> "AUTH" // MQTT 5.0
            else -> "UNKNOWN($messageType)"
        }
    }
    
    override fun parseProtocolData(rawData: ByteArray, context: ProtocolContext): String? {
        return try {
            parseMqttPacket(rawData)
        } catch (e: Exception) {
            logger.warn("解析MQTT数据失败: ${e.message}")
            null
        }
    }
    
    /**
     * 解析MQTT数据包
     */
    private fun parseMqttPacket(data: ByteArray): String {
        if (data.size < 2) {
            return "MQTT包: 数据不完整"
        }
        
        val firstByte = data[0].toInt() and 0xFF
        
        // 解析固定头部
        val messageType = (firstByte shr 4) and 0x0F
        val dup = (firstByte and 0x08) != 0
        val qos = (firstByte shr 1) and 0x03
        val retain = (firstByte and 0x01) != 0
        
        val messageTypeName = getMqttMessageTypeName(messageType)
        
        // 解析剩余长度
        val remainingLengthResult = parseRemainingLength(data, 1)
        val remainingLength = remainingLengthResult.first
        val remainingLengthBytes = remainingLengthResult.second
        
        if (remainingLength == -1) {
            return "MQTT包: 剩余长度解析失败"
        }
        
        val payloadStart = 1 + remainingLengthBytes
        
        return buildString {
            appendLine("MQTT包:")
            appendLine("  消息类型: $messageTypeName ($messageType)")
            appendLine("  DUP: $dup")
            appendLine("  QoS: $qos")
            appendLine("  RETAIN: $retain")
            appendLine("  剩余长度: $remainingLength 字节")
            
            // 根据消息类型解析特定内容
            when (messageType) {
                1 -> { // CONNECT
                    val connectInfo = parseConnectPacket(data, payloadStart, remainingLength)
                    append(connectInfo)
                }
                3 -> { // PUBLISH
                    val publishInfo = parsePublishPacket(data, payloadStart, remainingLength, qos)
                    append(publishInfo)
                }
                8 -> { // SUBSCRIBE
                    val subscribeInfo = parseSubscribePacket(data, payloadStart, remainingLength)
                    append(subscribeInfo)
                }
                else -> {
                    if (remainingLength > 0) {
                        appendLine("  载荷: ${remainingLength}字节")
                    }
                }
            }
        }
    }
    
    /**
     * 解析CONNECT包
     */
    private fun parseConnectPacket(data: ByteArray, start: Int, length: Int): String {
        var offset = start
        val result = StringBuilder()
        
        try {
            // 协议名称
            val protocolNameLength = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
            offset += 2
            val protocolName = String(data, offset, protocolNameLength, StandardCharsets.UTF_8)
            offset += protocolNameLength
            
            // 协议版本
            val protocolVersion = data[offset].toInt() and 0xFF
            offset++
            
            // 连接标志
            val connectFlags = data[offset].toInt() and 0xFF
            offset++
            
            val cleanSession = (connectFlags and 0x02) != 0
            val willFlag = (connectFlags and 0x04) != 0
            val willQos = (connectFlags shr 3) and 0x03
            val willRetain = (connectFlags and 0x20) != 0
            val passwordFlag = (connectFlags and 0x40) != 0
            val usernameFlag = (connectFlags and 0x80) != 0
            
            // 保持连接时间
            val keepAlive = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
            offset += 2
            
            result.appendLine("  协议名称: $protocolName")
            result.appendLine("  协议版本: $protocolVersion")
            result.appendLine("  清理会话: $cleanSession")
            result.appendLine("  保持连接: ${keepAlive}秒")
            result.appendLine("  用户名标志: $usernameFlag")
            result.appendLine("  密码标志: $passwordFlag")
            result.appendLine("  遗嘱标志: $willFlag")
            if (willFlag) {
                result.appendLine("  遗嘱QoS: $willQos")
                result.appendLine("  遗嘱保留: $willRetain")
            }
            
            // 客户端ID
            if (offset + 2 <= data.size) {
                val clientIdLength = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
                offset += 2
                if (offset + clientIdLength <= data.size) {
                    val clientId = String(data, offset, clientIdLength, StandardCharsets.UTF_8)
                    result.appendLine("  客户端ID: $clientId")
                }
            }
            
        } catch (e: Exception) {
            result.appendLine("  解析错误: ${e.message}")
        }
        
        return result.toString()
    }
    
    /**
     * 解析PUBLISH包
     */
    private fun parsePublishPacket(data: ByteArray, start: Int, length: Int, qos: Int): String {
        var offset = start
        val result = StringBuilder()
        
        try {
            // 主题名称
            val topicLength = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
            offset += 2
            val topic = String(data, offset, topicLength, StandardCharsets.UTF_8)
            offset += topicLength
            
            result.appendLine("  主题: $topic")
            
            // 包标识符（仅当QoS > 0时）
            if (qos > 0) {
                val packetId = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
                offset += 2
                result.appendLine("  包ID: $packetId")
            }
            
            // 载荷
            val payloadLength = length - (offset - start)
            if (payloadLength > 0) {
                val payload = ByteArray(payloadLength)
                System.arraycopy(data, offset, payload, 0, payloadLength)
                
                // 尝试解析为文本
                val payloadText = try {
                    String(payload, StandardCharsets.UTF_8)
                } catch (e: Exception) {
                    "二进制数据 (${payload.size}字节)"
                }
                
                result.appendLine("  载荷: ${payloadText.take(100)}${if (payloadText.length > 100) "..." else ""}")
            }
            
        } catch (e: Exception) {
            result.appendLine("  解析错误: ${e.message}")
        }
        
        return result.toString()
    }
    
    /**
     * 解析SUBSCRIBE包
     */
    private fun parseSubscribePacket(data: ByteArray, start: Int, length: Int): String {
        var offset = start
        val result = StringBuilder()
        
        try {
            // 包标识符
            val packetId = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
            offset += 2
            result.appendLine("  包ID: $packetId")
            
            // 主题过滤器
            result.appendLine("  订阅主题:")
            while (offset < start + length) {
                val topicLength = ((data[offset].toInt() and 0xFF) shl 8) or (data[offset + 1].toInt() and 0xFF)
                offset += 2
                val topic = String(data, offset, topicLength, StandardCharsets.UTF_8)
                offset += topicLength
                
                val requestedQos = data[offset].toInt() and 0xFF
                offset++
                
                result.appendLine("    $topic (QoS: $requestedQos)")
            }
            
        } catch (e: Exception) {
            result.appendLine("  解析错误: ${e.message}")
        }
        
        return result.toString()
    }
    
    override fun determineDirection(rawData: ByteArray, context: ProtocolContext): PacketData.PacketDirection {
        if (rawData.isEmpty()) {
            return PacketData.PacketDirection.UNKNOWN
        }
        
        val firstByte = rawData[0].toInt() and 0xFF
        val messageType = (firstByte shr 4) and 0x0F
        
        return when (messageType) {
            1, 3, 8, 10, 12, 14 -> PacketData.PacketDirection.CLIENT_TO_SERVER // CONNECT, PUBLISH, SUBSCRIBE, UNSUBSCRIBE, PINGREQ, DISCONNECT
            2, 4, 5, 6, 7, 9, 11, 13 -> PacketData.PacketDirection.SERVER_TO_CLIENT // CONNACK, PUBACK, PUBREC, PUBREL, PUBCOMP, SUBACK, UNSUBACK, PINGRESP
            else -> PacketData.PacketDirection.UNKNOWN
        }
    }
    
    override fun extractMetadata(rawData: ByteArray, context: ProtocolContext): Map<String, String> {
        val baseMetadata = super.extractMetadata(rawData, context).toMutableMap()
        
        try {
            if (rawData.isNotEmpty()) {
                val firstByte = rawData[0].toInt() and 0xFF
                val messageType = (firstByte shr 4) and 0x0F
                val qos = (firstByte shr 1) and 0x03
                val retain = (firstByte and 0x01) != 0
                val dup = (firstByte and 0x08) != 0
                
                baseMetadata["mqtt_message_type"] = messageType.toString()
                baseMetadata["mqtt_message_name"] = getMqttMessageTypeName(messageType)
                baseMetadata["mqtt_qos"] = qos.toString()
                baseMetadata["mqtt_retain"] = retain.toString()
                baseMetadata["mqtt_dup"] = dup.toString()
                
                // 解析剩余长度
                val remainingLengthResult = parseRemainingLength(rawData, 1)
                if (remainingLengthResult.first != -1) {
                    baseMetadata["mqtt_remaining_length"] = remainingLengthResult.first.toString()
                }
            }
            
        } catch (e: Exception) {
            logger.debug("提取MQTT元数据失败: ${e.message}")
        }
        
        return baseMetadata
    }
}
