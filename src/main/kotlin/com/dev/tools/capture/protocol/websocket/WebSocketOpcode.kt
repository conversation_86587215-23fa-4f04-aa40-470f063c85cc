package com.dev.tools.capture.protocol.websocket

import kotlinx.serialization.Serializable

/**
 * WebSocket操作码枚举
 */
@Serializable
enum class WebSocketOpcode(val value: Int, val description: String) {
    /**
     * 继续帧 - 表示这是一个分片消息的后续帧
     */
    CONTINUATION(0x0, "继续帧"),
    
    /**
     * 文本帧 - 包含UTF-8编码的文本数据
     */
    TEXT(0x1, "文本帧"),
    
    /**
     * 二进制帧 - 包含二进制数据
     */
    BINARY(0x2, "二进制帧"),
    
    /**
     * 关闭帧 - 用于关闭连接
     */
    CLOSE(0x8, "关闭帧"),
    
    /**
     * Ping帧 - 用于保持连接活跃
     */
    PING(0x9, "Ping帧"),
    
    /**
     * Pong帧 - 对Ping帧的响应
     */
    PONG(0xA, "Pong帧");
    
    /**
     * 是否为控制帧
     */
    val isControlFrame: Boolean
        get() = value >= 0x8
    
    /**
     * 是否为数据帧
     */
    val isDataFrame: Boolean
        get() = value < 0x8
    
    companion object {
        /**
         * 根据值获取操作码
         */
        fun fromValue(value: Int): WebSocketOpcode? {
            return values().find { it.value == value }
        }
        
        /**
         * 获取所有数据帧操作码
         */
        fun getDataFrameOpcodes(): List<WebSocketOpcode> {
            return values().filter { it.isDataFrame }
        }
        
        /**
         * 获取所有控制帧操作码
         */
        fun getControlFrameOpcodes(): List<WebSocketOpcode> {
            return values().filter { it.isControlFrame }
        }
    }
}

/**
 * WebSocket关闭状态码
 */
object WebSocketCloseCode {
    /** 正常关闭 */
    const val NORMAL_CLOSURE = 1000
    
    /** 端点离开 */
    const val GOING_AWAY = 1001
    
    /** 协议错误 */
    const val PROTOCOL_ERROR = 1002
    
    /** 不支持的数据类型 */
    const val UNSUPPORTED_DATA = 1003
    
    /** 保留 */
    const val RESERVED = 1004
    
    /** 没有状态码 */
    const val NO_STATUS_RECEIVED = 1005
    
    /** 异常关闭 */
    const val ABNORMAL_CLOSURE = 1006
    
    /** 无效的帧载荷数据 */
    const val INVALID_FRAME_PAYLOAD_DATA = 1007
    
    /** 策略违规 */
    const val POLICY_VIOLATION = 1008
    
    /** 消息太大 */
    const val MESSAGE_TOO_BIG = 1009
    
    /** 缺少扩展 */
    const val MANDATORY_EXTENSION = 1010
    
    /** 内部服务器错误 */
    const val INTERNAL_SERVER_ERROR = 1011
    
    /** 服务重启 */
    const val SERVICE_RESTART = 1012
    
    /** 稍后重试 */
    const val TRY_AGAIN_LATER = 1013
    
    /** 错误的网关 */
    const val BAD_GATEWAY = 1014
    
    /** TLS握手失败 */
    const val TLS_HANDSHAKE = 1015
    
    /**
     * 获取状态码描述
     */
    fun getDescription(code: Int): String {
        return when (code) {
            NORMAL_CLOSURE -> "正常关闭"
            GOING_AWAY -> "端点离开"
            PROTOCOL_ERROR -> "协议错误"
            UNSUPPORTED_DATA -> "不支持的数据类型"
            RESERVED -> "保留"
            NO_STATUS_RECEIVED -> "没有状态码"
            ABNORMAL_CLOSURE -> "异常关闭"
            INVALID_FRAME_PAYLOAD_DATA -> "无效的帧载荷数据"
            POLICY_VIOLATION -> "策略违规"
            MESSAGE_TOO_BIG -> "消息太大"
            MANDATORY_EXTENSION -> "缺少扩展"
            INTERNAL_SERVER_ERROR -> "内部服务器错误"
            SERVICE_RESTART -> "服务重启"
            TRY_AGAIN_LATER -> "稍后重试"
            BAD_GATEWAY -> "错误的网关"
            TLS_HANDSHAKE -> "TLS握手失败"
            in 1000..2999 -> "标准状态码"
            in 3000..3999 -> "库/框架保留"
            in 4000..4999 -> "应用程序保留"
            else -> "未知状态码"
        }
    }
    
    /**
     * 检查状态码是否有效
     */
    fun isValid(code: Int): Boolean {
        return when (code) {
            in 1000..1015 -> true
            in 3000..4999 -> true
            else -> false
        }
    }
}
