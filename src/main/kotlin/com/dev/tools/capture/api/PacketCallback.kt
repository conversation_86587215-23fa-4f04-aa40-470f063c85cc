package com.dev.tools.capture.api

import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.ProtocolContext

/**
 * 流量回调接口
 * 
 * 实现此接口以接收网络流量数据和错误通知
 */
interface PacketCallback {
    /**
     * 流量数据回调
     * 
     * 当捕获到网络数据包时调用此方法
     * 
     * @param packet 网络数据包
     * @param context 协议上下文信息
     */
    fun onPacketReceived(packet: PacketData, context: ProtocolContext)
    
    /**
     * 错误回调
     * 
     * 当处理过程中发生错误时调用此方法
     * 
     * @param exception 异常信息
     * @param context 协议上下文（可能为null）
     */
    fun onError(exception: CaptureException, context: ProtocolContext?)
    
    /**
     * 会话开始回调（可选实现）
     * 
     * 当新的网络会话开始时调用此方法
     * 
     * @param context 协议上下文
     */
    fun onSessionStarted(context: ProtocolContext) {
        // 默认空实现
    }
    
    /**
     * 会话结束回调（可选实现）
     * 
     * 当网络会话结束时调用此方法
     * 
     * @param context 协议上下文
     */
    fun onSessionEnded(context: ProtocolContext) {
        // 默认空实现
    }
    
    /**
     * SSL握手完成回调（可选实现）
     * 
     * 当SSL握手成功完成时调用此方法
     * 
     * @param context 协议上下文
     */
    fun onSslHandshakeCompleted(context: ProtocolContext) {
        // 默认空实现
    }
    
    /**
     * Mock规则匹配回调（可选实现）
     * 
     * 当数据包匹配Mock规则时调用此方法
     * 
     * @param packet 原始数据包
     * @param mockResponse Mock响应数据包
     * @param context 协议上下文
     * @param ruleId 匹配的规则ID
     */
    fun onMockRuleMatched(
        packet: PacketData,
        mockResponse: PacketData,
        context: ProtocolContext,
        ruleId: String
    ) {
        // 默认空实现
    }
}

/**
 * 抽象回调基类
 * 
 * 提供默认的错误处理实现，子类只需要实现 onPacketReceived 方法
 */
abstract class AbstractPacketCallback : PacketCallback {
    /**
     * 默认错误处理：记录错误日志
     */
    override fun onError(exception: CaptureException, context: ProtocolContext?) {
        val contextInfo = context?.let { 
            " [${it.protocol}:${it.session.id}]" 
        } ?: ""
        
        println("PacketCallback Error$contextInfo: ${exception.message}")
        exception.cause?.let {
            println("Caused by: ${it.message}")
        }
    }
}

/**
 * 简单的日志回调实现
 * 
 * 将所有事件输出到控制台，主要用于调试和测试
 */
class LoggingPacketCallback(
    private val logPackets: Boolean = true,
    private val logErrors: Boolean = true,
    private val logSessions: Boolean = false,
    private val logMockMatches: Boolean = true
) : PacketCallback {
    
    override fun onPacketReceived(packet: PacketData, context: ProtocolContext) {
        if (logPackets) {
            println("[PACKET] ${context.protocol} ${packet.direction} ${packet.size}字节 " +
                    "会话:${context.session.id}")
            if (packet.parsedData != null) {
                println("  解析数据: ${packet.parsedData}")
            }
        }
    }
    
    override fun onError(exception: CaptureException, context: ProtocolContext?) {
        if (logErrors) {
            val contextInfo = context?.let { 
                " [${it.protocol}:${it.session.id}]" 
            } ?: ""
            println("[ERROR]$contextInfo ${exception.errorCode}: ${exception.message}")
        }
    }
    
    override fun onSessionStarted(context: ProtocolContext) {
        if (logSessions) {
            println("[SESSION_START] ${context.protocol} ${context.session.source} -> ${context.session.destination}")
        }
    }
    
    override fun onSessionEnded(context: ProtocolContext) {
        if (logSessions) {
            val duration = context.session.duration?.let { "${it}ms" } ?: "未知"
            println("[SESSION_END] ${context.protocol} 会话:${context.session.id} 持续时间:$duration")
        }
    }
    
    override fun onSslHandshakeCompleted(context: ProtocolContext) {
        if (logSessions) {
            println("[SSL_HANDSHAKE] ${context.protocol} 会话:${context.session.id} SSL握手完成")
        }
    }
    
    override fun onMockRuleMatched(
        packet: PacketData,
        mockResponse: PacketData,
        context: ProtocolContext,
        ruleId: String
    ) {
        if (logMockMatches) {
            println("[MOCK_MATCH] 规则:$ruleId 会话:${context.session.id} " +
                    "响应:${mockResponse.size}字节")
        }
    }
}
