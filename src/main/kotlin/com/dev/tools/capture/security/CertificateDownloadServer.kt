package com.dev.tools.capture.security

import io.netty.bootstrap.ServerBootstrap
import io.netty.buffer.Unpooled
import io.netty.channel.*
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.SocketChannel
import io.netty.channel.socket.nio.NioServerSocketChannel
import io.netty.handler.codec.http.*
import io.netty.util.CharsetUtil
import org.slf4j.LoggerFactory
import java.net.InetAddress
import java.net.NetworkInterface

/**
 * SSL证书下载服务器
 * 
 * 提供HTTP服务让用户下载SSL根证书
 */
class CertificateDownloadServer(
    private val port: Int,
    private val certificateManager: CertificateManager
) {
    
    private val logger = LoggerFactory.getLogger(CertificateDownloadServer::class.java)
    private var serverChannel: Channel? = null
    private val bossGroup = NioEventLoopGroup(1)
    private val workerGroup = NioEventLoopGroup()
    
    /**
     * 启动证书下载服务器
     */
    fun start(): Boolean {
        return try {
            val bootstrap = ServerBootstrap()
            bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel::class.java)
                .childHandler(object : ChannelInitializer<SocketChannel>() {
                    override fun initChannel(ch: SocketChannel) {
                        val pipeline = ch.pipeline()
                        pipeline.addLast("httpCodec", HttpServerCodec())
                        pipeline.addLast("httpAggregator", HttpObjectAggregator(65536))
                        pipeline.addLast("httpHandler", CertificateHttpHandler())
                    }
                })
                .option(ChannelOption.SO_BACKLOG, 128)
                .childOption(ChannelOption.SO_KEEPALIVE, true)
            
            val future = bootstrap.bind(port).sync()
            serverChannel = future.channel()
            
            logger.info("证书下载服务器已启动，端口: $port")
            printDownloadInstructions()
            
            true
        } catch (e: Exception) {
            logger.error("启动证书下载服务器失败", e)
            false
        }
    }
    
    /**
     * 停止证书下载服务器
     */
    fun stop() {
        try {
            serverChannel?.close()?.sync()
            workerGroup.shutdownGracefully()
            bossGroup.shutdownGracefully()
            logger.info("证书下载服务器已停止")
        } catch (e: Exception) {
            logger.error("停止证书下载服务器失败", e)
        }
    }
    
    /**
     * 打印下载说明
     */
    private fun printDownloadInstructions() {
        val localIPs = getLocalIPAddresses()
        
        println("\n📜 SSL证书下载说明:")
        println("   本机访问: http://127.0.0.1:$port/certificate")
        println("   Android专用: http://127.0.0.1:$port/certificate-pem")

        if (localIPs.isNotEmpty()) {
            println("   手机访问: http://${localIPs.first()}:$port/certificate")
            println("   Android专用: http://${localIPs.first()}:$port/certificate-pem")
            localIPs.forEach { ip ->
                println("             http://$ip:$port/certificate")
                println("             http://$ip:$port/certificate-pem (Android)")
            }
        }
        
        println("\n📱 手机安装证书步骤:")
        println("   1. 在手机浏览器中访问上述地址")
        println("   2. 下载 root-ca.crt 证书文件")
        println("   3. 安装证书:")
        println("      Android: 设置 > 安全 > 加密和凭据 > 安装证书")
        println("      iOS: 设置 > 通用 > VPN与设备管理 > 安装描述文件")
        println("   4. 信任证书 (iOS需要额外在设置 > 通用 > 关于本机 > 证书信任设置中启用)")
        println()
    }
    
    /**
     * 获取本机IP地址
     */
    private fun getLocalIPAddresses(): List<String> {
        val addresses = mutableListOf<String>()
        
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                
                if (networkInterface.isLoopback || !networkInterface.isUp) {
                    continue
                }
                
                val inetAddresses = networkInterface.inetAddresses
                while (inetAddresses.hasMoreElements()) {
                    val inetAddress = inetAddresses.nextElement()
                    
                    if (!inetAddress.isLoopbackAddress && 
                        !inetAddress.isLinkLocalAddress && 
                        inetAddress is java.net.Inet4Address) {
                        addresses.add(inetAddress.hostAddress)
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("获取IP地址失败", e)
        }
        
        return addresses
    }
    
    /**
     * HTTP处理器
     */
    private inner class CertificateHttpHandler : SimpleChannelInboundHandler<FullHttpRequest>() {
        
        override fun channelRead0(ctx: ChannelHandlerContext, request: FullHttpRequest) {
            val uri = request.uri()
            val method = request.method()
            
            logger.debug("收到请求: $method $uri")
            
            val response = when {
                uri == "/" || uri == "/index" -> handleIndexPage()
                uri == "/certificate" || uri == "/cert" -> handleCertificateDownload("crt")
                uri == "/certificate-pem" || uri == "/cert-pem" -> handleCertificateDownload("pem")
                uri == "/info" -> handleCertificateInfo()
                else -> handleNotFound()
            }
            
            ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE)
        }
        
        /**
         * 处理首页
         */
        private fun handleIndexPage(): FullHttpResponse {
            val html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>PacketCapture SSL证书下载</title>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        h1 { color: #2196F3; text-align: center; }
                        .download-btn { display: inline-block; background: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
                        .download-btn:hover { background: #45a049; }
                        .info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
                        .steps { background: #fff3e0; padding: 15px; border-radius: 5px; margin: 20px 0; }
                        ol { padding-left: 20px; }
                        li { margin: 10px 0; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🔒 SSL证书下载中心</h1>
                        
                        <div class="info">
                            <h3>📋 说明</h3>
                            <p>为了拦截HTTPS流量，您需要安装并信任我们的根证书。</p>
                            <p>这是一个自签名证书，仅用于开发和测试目的。</p>
                        </div>
                        
                        <div style="text-align: center;">
                            <a href="/certificate" class="download-btn">📥 下载根证书 (.crt格式)</a>
                            <br>
                            <a href="/certificate-pem" class="download-btn" style="background: #FF9800;">📱 Android专用 (.pem格式)</a>
                        </div>
                        
                        <div class="steps">
                            <h3>📱 安装步骤</h3>
                            <h4>Android:</h4>
                            <ol>
                                <li><strong>点击上方橙色按钮下载 .pem 格式证书</strong></li>
                                <li>打开 设置 > 安全 > 加密和凭据</li>
                                <li>选择 "安装证书" > "CA证书"</li>
                                <li>选择下载的 root-ca.pem 文件</li>
                                <li>设置证书名称: "PacketCapture Root CA"</li>
                                <li>确认安装并输入锁屏密码</li>
                            </ol>
                            <p style="color: #FF5722;"><strong>注意:</strong> Android系统推荐使用 .pem 格式的证书文件</p>
                            
                            <h4>iOS:</h4>
                            <ol>
                                <li>下载证书文件</li>
                                <li>打开 设置 > 通用 > VPN与设备管理</li>
                                <li>在"已下载的描述文件"中找到证书</li>
                                <li>点击安装并输入密码</li>
                                <li>前往 设置 > 通用 > 关于本机 > 证书信任设置</li>
                                <li>启用对根证书的完全信任</li>
                            </ol>
                        </div>
                        
                        <div style="text-align: center; margin-top: 30px;">
                            <a href="/info">📊 查看证书信息</a>
                        </div>
                    </div>
                </body>
                </html>
            """.trimIndent()
            
            return createHttpResponse(HttpResponseStatus.OK, "text/html", html)
        }
        
        /**
         * 处理证书下载
         */
        private fun handleCertificateDownload(format: String = "crt"): FullHttpResponse {
            val (certContent, filename, contentType) = when (format) {
                "pem" -> {
                    val content = certificateManager.getRootCACertificateContent("pem")
                    Triple(content, "root-ca.pem", "application/x-pem-file")
                }
                else -> {
                    val content = certificateManager.getRootCACertificateContent("crt")
                    Triple(content, "root-ca.crt", "application/x-x509-ca-cert")
                }
            }

            return if (certContent != null) {
                val response = createHttpResponse(HttpResponseStatus.OK, contentType, certContent)
                response.headers().set("Content-Disposition", "attachment; filename=\"$filename\"")
                response
            } else {
                createHttpResponse(HttpResponseStatus.NOT_FOUND, "text/plain", "证书文件未找到")
            }
        }
        
        /**
         * 处理证书信息查询
         */
        private fun handleCertificateInfo(): FullHttpResponse {
            val info = certificateManager.getCertificateInfo()
            val json = info.entries.joinToString(",\n  ", "{\n  ", "\n}") { (key, value) ->
                "\"$key\": \"$value\""
            }
            
            return createHttpResponse(HttpResponseStatus.OK, "application/json", json)
        }
        
        /**
         * 处理404
         */
        private fun handleNotFound(): FullHttpResponse {
            return createHttpResponse(HttpResponseStatus.NOT_FOUND, "text/plain", "页面未找到")
        }
        
        /**
         * 创建HTTP响应
         */
        private fun createHttpResponse(status: HttpResponseStatus, contentType: String, content: String): FullHttpResponse {
            val buffer = Unpooled.copiedBuffer(content, CharsetUtil.UTF_8)
            val response = DefaultFullHttpResponse(HttpVersion.HTTP_1_1, status, buffer)
            
            response.headers().set(HttpHeaderNames.CONTENT_TYPE, contentType)
            response.headers().set(HttpHeaderNames.CONTENT_LENGTH, buffer.readableBytes())
            response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE)
            
            return response
        }
        
        override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
            logger.warn("证书下载服务器处理请求时发生异常", cause)
            ctx.close()
        }
    }
}
