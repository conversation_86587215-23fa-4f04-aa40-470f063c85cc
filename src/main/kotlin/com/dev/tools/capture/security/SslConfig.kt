package com.dev.tools.capture.security

import kotlinx.serialization.Serializable

/**
 * SSL配置
 */
@Serializable
data class SslConfig(
    /**
     * 是否启用SSL中间人解密
     */
    val enableMitm: Boolean = true,
    
    /**
     * 支持的SSL/TLS协议版本
     */
    val supportedProtocols: Set<String> = setOf("TLSv1.2", "TLSv1.3"),
    
    /**
     * 支持的加密套件
     */
    val supportedCipherSuites: Set<String> = setOf(
        "TLS_AES_256_GCM_SHA384",
        "TLS_AES_128_GCM_SHA256",
        "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
        "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
    ),
    
    /**
     * 证书有效期（天）
     */
    val certificateValidityDays: Long = 365,
    
    /**
     * 密钥长度
     */
    val keyLength: Int = 2048,
    
    /**
     * 是否验证服务器证书
     */
    val verifyServerCertificates: Boolean = false,
    
    /**
     * 是否验证客户端证书
     */
    val verifyClientCertificates: Boolean = false,
    
    /**
     * SSL握手超时时间（毫秒）
     */
    val handshakeTimeoutMs: Long = 30000,
    
    /**
     * 是否启用会话复用
     */
    val enableSessionReuse: Boolean = true,
    
    /**
     * 会话缓存大小
     */
    val sessionCacheSize: Int = 1000,
    
    /**
     * 会话超时时间（秒）
     */
    val sessionTimeoutSeconds: Int = 3600,
    
    /**
     * 是否记录SSL调试信息
     */
    val enableDebugLogging: Boolean = false,
    
    /**
     * 自定义属性
     */
    val customAttributes: Map<String, String> = emptyMap()
) {
    
    /**
     * 验证配置的有效性
     */
    fun validate(): List<String> {
        val errors = mutableListOf<String>()
        
        if (supportedProtocols.isEmpty()) {
            errors.add("至少需要支持一种SSL/TLS协议")
        }
        
        if (supportedCipherSuites.isEmpty()) {
            errors.add("至少需要支持一种加密套件")
        }
        
        if (certificateValidityDays <= 0) {
            errors.add("证书有效期必须大于0天")
        }
        
        if (keyLength < 1024) {
            errors.add("密钥长度不能少于1024位")
        }
        
        if (handshakeTimeoutMs <= 0) {
            errors.add("SSL握手超时时间必须大于0")
        }
        
        if (sessionCacheSize <= 0) {
            errors.add("会话缓存大小必须大于0")
        }
        
        if (sessionTimeoutSeconds <= 0) {
            errors.add("会话超时时间必须大于0")
        }
        
        return errors
    }
    
    /**
     * 是否为有效配置
     */
    val isValid: Boolean
        get() = validate().isEmpty()
    
    companion object {
        /**
         * 创建默认配置
         */
        fun default(): SslConfig {
            return SslConfig()
        }
        
        /**
         * 创建高安全性配置
         */
        fun highSecurity(): SslConfig {
            return SslConfig(
                supportedProtocols = setOf("TLSv1.3"),
                supportedCipherSuites = setOf(
                    "TLS_AES_256_GCM_SHA384",
                    "TLS_CHACHA20_POLY1305_SHA256"
                ),
                verifyServerCertificates = true,
                verifyClientCertificates = true,
                keyLength = 4096,
                enableDebugLogging = false
            )
        }
        
        /**
         * 创建开发环境配置
         */
        fun development(): SslConfig {
            return SslConfig(
                verifyServerCertificates = false,
                verifyClientCertificates = false,
                enableDebugLogging = true,
                handshakeTimeoutMs = 60000 // 更长的超时时间用于调试
            )
        }
        
        /**
         * 创建性能优化配置
         */
        fun performance(): SslConfig {
            return SslConfig(
                enableSessionReuse = true,
                sessionCacheSize = 5000,
                sessionTimeoutSeconds = 7200,
                handshakeTimeoutMs = 10000 // 更短的超时时间
            )
        }
    }
}

/**
 * SSL统计信息
 */
data class SslStatistics(
    /**
     * 成功的SSL握手次数
     */
    val successfulHandshakes: Long = 0,
    
    /**
     * 失败的SSL握手次数
     */
    val failedHandshakes: Long = 0,
    
    /**
     * 当前活跃的SSL会话数
     */
    val activeSessions: Int = 0,
    
    /**
     * 缓存的证书数量
     */
    val cachedCertificates: Int = 0,
    
    /**
     * 平均握手时间（毫秒）
     */
    val averageHandshakeTimeMs: Double = 0.0,
    
    /**
     * 使用的协议版本统计
     */
    val protocolVersionStats: Map<String, Long> = emptyMap(),
    
    /**
     * 使用的加密套件统计
     */
    val cipherSuiteStats: Map<String, Long> = emptyMap(),
    
    /**
     * 错误统计
     */
    val errorStats: Map<String, Long> = emptyMap()
) {
    /**
     * 握手成功率
     */
    val handshakeSuccessRate: Double
        get() {
            val total = successfulHandshakes + failedHandshakes
            return if (total > 0) successfulHandshakes.toDouble() / total else 0.0
        }
    
    /**
     * 转换为映射格式
     */
    fun toMap(): Map<String, Any> {
        return mapOf(
            "successfulHandshakes" to successfulHandshakes,
            "failedHandshakes" to failedHandshakes,
            "handshakeSuccessRate" to handshakeSuccessRate,
            "activeSessions" to activeSessions,
            "cachedCertificates" to cachedCertificates,
            "averageHandshakeTimeMs" to averageHandshakeTimeMs,
            "protocolVersionStats" to protocolVersionStats,
            "cipherSuiteStats" to cipherSuiteStats,
            "errorStats" to errorStats
        )
    }
}
