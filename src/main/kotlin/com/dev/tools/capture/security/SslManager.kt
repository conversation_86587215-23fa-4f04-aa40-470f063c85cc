package com.dev.tools.capture.security

import com.dev.tools.capture.exception.SslCertificateException
import com.dev.tools.capture.exception.SslDecryptionException
import com.dev.tools.capture.model.ProtocolContext
import org.slf4j.LoggerFactory
import java.security.KeyStore
import java.security.cert.X509Certificate
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import java.util.concurrent.ConcurrentHashMap

/**
 * SSL管理器
 * 
 * 负责SSL/TLS证书管理、中间人解密等安全功能
 */
class SslManager {
    
    private val logger = LoggerFactory.getLogger(SslManager::class.java)
    
    // SSL上下文缓存
    private val sslContextCache = ConcurrentHashMap<String, SSLContext>()
    
    // 根证书提供者
    private val certificateProvider = CertificateProvider()
    
    /**
     * 初始化SSL管理器
     */
    fun initialize() {
        try {
            // 清除SSL上下文缓存，确保重新生成
            sslContextCache.clear()
            logger.debug("已清除SSL上下文缓存")

            // 生成根证书
            certificateProvider.generateRootCertificate()
            logger.info("SSL管理器初始化完成")
        } catch (e: Exception) {
            logger.error("SSL管理器初始化失败", e)
            throw SslDecryptionException.handshakeFailed("SSL管理器初始化失败", cause = e)
        }
    }
    
    /**
     * 为指定主机名创建SSL上下文
     * 
     * @param hostname 主机名
     * @return SSL上下文
     */
    fun createSslContext(hostname: String): SSLContext {
        return sslContextCache.computeIfAbsent(hostname) { host ->
            try {
                logger.debug("为主机创建SSL上下文: $host")
                
                // 生成服务器证书
                val serverCert = certificateProvider.generateServerCertificate(host)
                
                // 创建SSL上下文
                val sslContext = SSLContext.getInstance("TLS")
                
                // 创建信任所有证书的TrustManager
                val trustManager = object : X509TrustManager {
                    override fun checkClientTrusted(chain: Array<X509Certificate>, authType: String) {
                        // 信任所有客户端证书
                    }
                    
                    override fun checkServerTrusted(chain: Array<X509Certificate>, authType: String) {
                        // 信任所有服务器证书
                    }
                    
                    override fun getAcceptedIssuers(): Array<X509Certificate> {
                        return arrayOf()
                    }
                }
                
                sslContext.init(
                    certificateProvider.getKeyManagers(serverCert, host),
                    arrayOf<TrustManager>(trustManager),
                    null
                )
                
                sslContext
                
            } catch (e: Exception) {
                logger.error("创建SSL上下文失败: $host", e)
                throw SslDecryptionException.handshakeFailed("创建SSL上下文失败", cause = e)
            }
        }
    }
    
    /**
     * 获取主机的证书
     *
     * @param hostname 主机名
     * @return 证书，如果不存在则返回null
     */
    fun getCertificate(hostname: String): X509Certificate? {
        return try {
            certificateProvider.generateServerCertificate(hostname)
        } catch (e: Exception) {
            logger.warn("获取证书失败: $hostname", e)
            null
        }
    }
    
    /**
     * 验证SSL握手
     * 
     * @param hostname 主机名
     * @param context 协议上下文
     * @return 是否验证成功
     */
    fun validateSslHandshake(hostname: String, context: ProtocolContext): Boolean {
        return try {
            val certificate = getCertificate(hostname)
            if (certificate == null) {
                logger.warn("未找到主机证书: $hostname")
                return false
            }
            
            // 验证证书有效期
            certificate.checkValidity()
            
            // 验证主机名匹配
            if (!validateHostname(certificate, hostname)) {
                logger.warn("主机名不匹配: $hostname")
                return false
            }
            
            logger.debug("SSL握手验证成功: $hostname")
            true
            
        } catch (e: Exception) {
            logger.warn("SSL握手验证失败: $hostname", e)
            false
        }
    }
    
    /**
     * 验证主机名是否匹配证书
     */
    private fun validateHostname(certificate: X509Certificate, hostname: String): Boolean {
        return try {
            // 获取证书的主题备用名称
            val subjectAltNames = certificate.subjectAlternativeNames
            if (subjectAltNames != null) {
                for (altName in subjectAltNames) {
                    if (altName.size >= 2 && altName[0] == 2) { // DNS名称
                        val dnsName = altName[1] as String
                        if (matchesHostname(dnsName, hostname)) {
                            return true
                        }
                    }
                }
            }
            
            // 检查证书的通用名称
            val subjectDN = certificate.subjectX500Principal.name
            val cnPattern = Regex("CN=([^,]+)")
            val cnMatch = cnPattern.find(subjectDN)
            if (cnMatch != null) {
                val commonName = cnMatch.groupValues[1]
                return matchesHostname(commonName, hostname)
            }
            
            false
        } catch (e: Exception) {
            logger.debug("验证主机名时发生错误", e)
            false
        }
    }
    
    /**
     * 检查主机名是否匹配（支持通配符）
     */
    private fun matchesHostname(pattern: String, hostname: String): Boolean {
        if (pattern == hostname) {
            return true
        }
        
        // 支持通配符匹配
        if (pattern.startsWith("*.")) {
            val domain = pattern.substring(2)
            return hostname.endsWith(".$domain") || hostname == domain
        }
        
        return false
    }
    
    /**
     * 处理SSL解密失败的情况
     * 
     * @param hostname 主机名
     * @param context 协议上下文
     * @param cause 失败原因
     */
    fun handleSslDecryptionFailure(hostname: String, context: ProtocolContext, cause: Throwable) {
        logger.warn("SSL解密失败: $hostname", cause)
        
        // 清除缓存的证书和SSL上下文
        certificateProvider.clearCertificateCache(hostname)
        sslContextCache.remove(hostname)
        
        // 可以在这里实现降级策略
        // 例如：切换到隧道模式
    }
    
    /**
     * 获取根证书（用于客户端安装）
     * 
     * @return 根证书的PEM格式字符串
     */
    fun getRootCertificatePem(): String {
        return certificateProvider.getRootCertificatePem()
    }
    
    /**
     * 获取SSL统计信息
     *
     * @return 统计信息映射
     */
    fun getStatistics(): Map<String, Any> {
        return mapOf(
            "cachedSslContexts" to sslContextCache.size,
            "rootCertificateGenerated" to certificateProvider.isRootCertificateGenerated()
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        sslContextCache.clear()
        certificateProvider.cleanup()
        logger.info("SSL管理器已清理")
    }
}
