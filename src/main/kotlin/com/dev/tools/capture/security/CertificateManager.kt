package com.dev.tools.capture.security

import org.bouncycastle.asn1.x500.X500Name
import org.bouncycastle.asn1.x509.*
import org.bouncycastle.cert.X509CertificateHolder
import org.bouncycastle.cert.X509v3CertificateBuilder
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter
import org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.operator.ContentSigner
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileOutputStream
import java.io.FileWriter
import java.math.BigInteger
import java.security.KeyPair
import java.security.KeyPairGenerator
import java.security.KeyStore
import java.security.Security
import java.security.cert.X509Certificate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*

/**
 * SSL证书管理器
 * 
 * 负责生成、存储和管理SSL证书
 */
class CertificateManager(
    private val certificatePath: String,
    private val keyStorePath: String,
    private val keyStorePassword: String
) {
    
    private val logger = LoggerFactory.getLogger(CertificateManager::class.java)
    
    companion object {
        private const val KEY_ALGORITHM = "RSA"
        private const val KEY_SIZE = 2048
        private const val SIGNATURE_ALGORITHM = "SHA256withRSA"
        private const val CERTIFICATE_VALIDITY_DAYS = 365
        private const val ROOT_CA_ALIAS = "root-ca"
        private const val SERVER_CERT_ALIAS = "server-cert"
    }
    
    /**
     * 初始化证书管理器
     */
    fun initialize(): Boolean {
        return try {
            // 添加BouncyCastle提供者
            if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
                Security.addProvider(BouncyCastleProvider())
                logger.info("已添加BouncyCastle安全提供者")
            }

            // 创建证书目录
            createDirectories()

            // 检查是否已有证书
            if (!hasCertificates()) {
                logger.info("未找到SSL证书，开始生成真实证书...")
                generateRealCertificates()
            } else {
                logger.info("SSL证书已存在，跳过生成")
            }

            true
        } catch (e: Exception) {
            logger.error("初始化证书管理器失败", e)
            false
        }
    }
    
    /**
     * 创建必要的目录
     */
    private fun createDirectories() {
        try {
            // 确保使用相对路径，避免权限问题
            val workingDir = File(System.getProperty("user.dir"))
            val certDir = if (certificatePath.startsWith("/")) {
                // 如果是绝对路径，转换为相对路径
                File(workingDir, certificatePath.removePrefix("/"))
            }else if (certificatePath.startsWith(".")) {
                // 如果是绝对路径，转换为相对路径
                File(certificatePath)
            } else {
                File(workingDir, certificatePath)
            }

            logger.info("工作目录: ${workingDir.absolutePath}")
            logger.info("准备创建证书目录: ${certDir.absolutePath}")

            if (!certDir.exists()) {
                val created = certDir.mkdirs()
                if (created) {
                    logger.info("创建证书目录成功: ${certDir.absolutePath}")
                } else {
                    logger.error("无法创建证书目录: ${certDir.absolutePath}")
                    // 检查父目录权限
                    val parent = certDir.parentFile
                    if (parent != null) {
                        logger.error("父目录存在: ${parent.exists()}, 可写: ${parent.canWrite()}")
                    }
                    throw RuntimeException("无法创建证书目录: ${certDir.absolutePath}")
                }
            } else {
                logger.info("证书目录已存在: ${certDir.absolutePath}")
            }

            // 处理密钥库目录
            val keyStoreFile = if (keyStorePath.startsWith("/")) {
                File(workingDir, keyStorePath.removePrefix("/"))
            } else {
                File(workingDir, keyStorePath)
            }
            val keyStoreDir = keyStoreFile.parentFile
            logger.info("准备创建密钥库目录: ${keyStoreDir?.absolutePath}")

            if (keyStoreDir != null && !keyStoreDir.exists()) {
                val created = keyStoreDir.mkdirs()
                if (created) {
                    logger.info("创建密钥库目录成功: ${keyStoreDir.absolutePath}")
                } else {
                    logger.error("无法创建密钥库目录: ${keyStoreDir.absolutePath}")
                    throw RuntimeException("无法创建密钥库目录: ${keyStoreDir.absolutePath}")
                }
            } else if (keyStoreDir != null) {
                logger.info("密钥库目录已存在: ${keyStoreDir.absolutePath}")
            }
        } catch (e: Exception) {
            logger.error("创建目录时发生错误", e)
            throw e
        }
    }
    
    /**
     * 检查是否已有证书
     */
    private fun hasCertificates(): Boolean {
        val keyStoreFile = File(keyStorePath)
        val rootCertFile = File(certificatePath, "root-ca.crt")
        return keyStoreFile.exists() && rootCertFile.exists()
    }
    
    /**
     * 生成真实的SSL证书
     */
    private fun generateRealCertificates() {
        logger.info("开始生成真实SSL证书...")

        try {
            // 1. 生成根CA密钥对
            val rootKeyPair = generateKeyPair()
            logger.info("生成根CA密钥对完成")

            // 2. 生成根CA证书
            val rootCert = generateRootCACertificate(rootKeyPair)
            logger.info("生成根CA证书完成")

            // 3. 生成服务器密钥对
            val serverKeyPair = generateKeyPair()
            logger.info("生成服务器密钥对完成")

            // 4. 生成服务器证书
            val serverCert = generateServerCertificate(serverKeyPair, rootKeyPair, rootCert)
            logger.info("生成服务器证书完成")

            // 5. 保存证书到文件
            saveCertificateToFile(rootCert, "root-ca.crt")
            saveCertificateToFile(rootCert, "root-ca.pem")  // Android兼容格式
            saveCertificateToFile(serverCert, "server.crt")
            saveCertificateToFile(serverCert, "server.pem")

            // 6. 创建密钥库
            createKeyStore(rootKeyPair, rootCert, serverKeyPair, serverCert)

            logger.info("SSL证书生成完成")
            logger.info("根CA证书: ${File(certificatePath, "root-ca.crt").absolutePath}")
            logger.info("服务器证书: ${File(certificatePath, "server.crt").absolutePath}")
            logger.info("密钥库: ${File(keyStorePath).absolutePath}")

        } catch (e: Exception) {
            logger.error("生成SSL证书失败", e)
            throw e
        }
    }
    
    /**
     * 生成密钥对
     */
    private fun generateKeyPair(): KeyPair {
        val keyPairGenerator = KeyPairGenerator.getInstance(KEY_ALGORITHM)
        keyPairGenerator.initialize(KEY_SIZE)
        return keyPairGenerator.generateKeyPair()
    }
    
    /**
     * 生成根CA证书
     */
    private fun generateRootCACertificate(keyPair: KeyPair): X509Certificate {
        logger.info("生成根CA证书...")

        val now = Date()
        val notBefore = Date(now.time - 24 * 60 * 60 * 1000) // 昨天
        val notAfter = Date(now.time + CERTIFICATE_VALIDITY_DAYS * 24 * 60 * 60 * 1000L) // 一年后

        val serialNumber = BigInteger.valueOf(System.currentTimeMillis())
        val subject = X500Name("CN=PacketCapture Root CA, O=PacketCapture SDK, C=CN")

        val certBuilder = JcaX509v3CertificateBuilder(
            subject, // issuer (自签名，所以issuer和subject相同)
            serialNumber,
            notBefore,
            notAfter,
            subject, // subject
            keyPair.public
        )

        // 添加扩展
        certBuilder.addExtension(Extension.basicConstraints, true, BasicConstraints(true))
        certBuilder.addExtension(Extension.keyUsage, true,
            KeyUsage(KeyUsage.keyCertSign or KeyUsage.cRLSign or KeyUsage.digitalSignature))

        val contentSigner: ContentSigner = JcaContentSignerBuilder(SIGNATURE_ALGORITHM)
            .setProvider(BouncyCastleProvider.PROVIDER_NAME)
            .build(keyPair.private)

        val certHolder: X509CertificateHolder = certBuilder.build(contentSigner)

        return JcaX509CertificateConverter()
            .setProvider(BouncyCastleProvider.PROVIDER_NAME)
            .getCertificate(certHolder)
    }
    
    /**
     * 生成服务器证书
     */
    private fun generateServerCertificate(
        serverKeyPair: KeyPair,
        caKeyPair: KeyPair,
        caCert: X509Certificate
    ): X509Certificate {
        logger.info("生成服务器证书...")

        val now = Date()
        val notBefore = Date(now.time - 24 * 60 * 60 * 1000) // 昨天
        val notAfter = Date(now.time + CERTIFICATE_VALIDITY_DAYS * 24 * 60 * 60 * 1000L) // 一年后

        val serialNumber = BigInteger.valueOf(System.currentTimeMillis() + 1)
        val issuer = X500Name(caCert.subjectX500Principal.name)
        val subject = X500Name("CN=PacketCapture Server, O=PacketCapture SDK, C=CN")

        val certBuilder = JcaX509v3CertificateBuilder(
            issuer, // CA作为issuer
            serialNumber,
            notBefore,
            notAfter,
            subject, // 服务器subject
            serverKeyPair.public
        )

        // 添加服务器证书扩展
        certBuilder.addExtension(Extension.basicConstraints, true, BasicConstraints(false))
        certBuilder.addExtension(Extension.keyUsage, true,
            KeyUsage(KeyUsage.digitalSignature or KeyUsage.keyEncipherment))

        // 添加SAN (Subject Alternative Names) 扩展，支持多个域名
        val sanBuilder = GeneralNamesBuilder()
        sanBuilder.addName(GeneralName(GeneralName.dNSName, "localhost"))
        sanBuilder.addName(GeneralName(GeneralName.dNSName, "127.0.0.1"))
        sanBuilder.addName(GeneralName(GeneralName.dNSName, "*.local"))
        sanBuilder.addName(GeneralName(GeneralName.iPAddress, "127.0.0.1"))
        sanBuilder.addName(GeneralName(GeneralName.iPAddress, "::1"))

        certBuilder.addExtension(Extension.subjectAlternativeName, false, sanBuilder.build())

        val contentSigner: ContentSigner = JcaContentSignerBuilder(SIGNATURE_ALGORITHM)
            .setProvider(BouncyCastleProvider.PROVIDER_NAME)
            .build(caKeyPair.private) // 使用CA私钥签名

        val certHolder: X509CertificateHolder = certBuilder.build(contentSigner)

        return JcaX509CertificateConverter()
            .setProvider(BouncyCastleProvider.PROVIDER_NAME)
            .getCertificate(certHolder)
    }
    
    /**
     * 保存证书到文件
     */
    private fun saveCertificateToFile(certificate: X509Certificate, filename: String) {
        // 确保使用相对路径
        val workingDir = File(System.getProperty("user.dir"))
        val certDir = if (certificatePath.startsWith("/")) {
            File(workingDir, certificatePath.removePrefix("/"))
        } else {
            File(workingDir, certificatePath)
        }
        val certFile = File(certDir, filename)

        // 确保父目录存在
        certFile.parentFile?.let { parentDir ->
            if (!parentDir.exists()) {
                if (parentDir.mkdirs()) {
                    logger.info("创建证书目录: ${parentDir.absolutePath}")
                } else {
                    throw RuntimeException("无法创建证书目录: ${parentDir.absolutePath}")
                }
            }
        }

        // 保存为PEM格式
        val encodedCert = Base64.getEncoder().encodeToString(certificate.encoded)
        val pemContent = buildString {
            appendLine("-----BEGIN CERTIFICATE-----")
            // 每64个字符换行
            encodedCert.chunked(64).forEach { chunk ->
                appendLine(chunk)
            }
            appendLine("-----END CERTIFICATE-----")
        }

        try {
            FileWriter(certFile).use { writer ->
                writer.write(pemContent)
            }
            logger.info("证书已保存: ${certFile.absolutePath}")
            logger.info("证书主题: ${certificate.subjectX500Principal.name}")
            logger.info("证书有效期: ${certificate.notBefore} 到 ${certificate.notAfter}")
        } catch (e: Exception) {
            logger.error("保存证书文件失败: ${certFile.absolutePath}", e)
            throw e
        }
    }
    
    /**
     * 创建密钥库
     */
    private fun createKeyStore(
        rootKeyPair: KeyPair,
        rootCert: X509Certificate,
        serverKeyPair: KeyPair,
        serverCert: X509Certificate
    ) {
        val keyStore = KeyStore.getInstance("PKCS12")
        keyStore.load(null, keyStorePassword.toCharArray())

        try {
            // 验证证书链
            serverCert.verify(rootCert.publicKey)
            logger.info("证书链验证成功")

            // 添加根CA证书作为信任锚点
            keyStore.setCertificateEntry(ROOT_CA_ALIAS, rootCert)

            // 添加服务器证书和私钥，证书链顺序：服务器证书 -> 根CA证书
            val certChain = arrayOf(serverCert, rootCert)
            keyStore.setKeyEntry(
                SERVER_CERT_ALIAS,
                serverKeyPair.private,
                keyStorePassword.toCharArray(),
                certChain
            )

            // 保存密钥库
            FileOutputStream(keyStorePath).use { fos ->
                keyStore.store(fos, keyStorePassword.toCharArray())
            }

            logger.info("密钥库已创建: $keyStorePath")

        } catch (e: Exception) {
            logger.error("创建密钥库失败", e)

            // 如果证书链有问题，只保存根CA证书
            logger.info("尝试创建简化的密钥库...")
            val simpleKeyStore = KeyStore.getInstance("PKCS12")
            simpleKeyStore.load(null, keyStorePassword.toCharArray())

            // 只添加根CA证书和私钥
            simpleKeyStore.setKeyEntry(
                ROOT_CA_ALIAS,
                rootKeyPair.private,
                keyStorePassword.toCharArray(),
                arrayOf(rootCert)
            )

            FileOutputStream(keyStorePath).use { fos ->
                simpleKeyStore.store(fos, keyStorePassword.toCharArray())
            }

            logger.info("简化密钥库已创建: $keyStorePath")
        }
    }
    
    /**
     * 获取根CA证书文件路径
     */
    fun getRootCACertificatePath(): String {
        return File(certificatePath, "root-ca.crt").absolutePath
    }
    
    /**
     * 获取根CA证书内容
     */
    fun getRootCACertificateContent(format: String = "crt"): String? {
        return try {
            val filename = when (format.lowercase()) {
                "pem" -> "root-ca.pem"
                else -> "root-ca.crt"
            }

            // 确保使用相对路径
            val workingDir = File(System.getProperty("user.dir"))
            val certDir = if (certificatePath.startsWith("/")) {
                File(workingDir, certificatePath.removePrefix("/"))
            } else {
                File(workingDir, certificatePath)
            }
            val certFile = File(certDir, filename)

            if (certFile.exists()) {
                certFile.readText()
            } else {
                null
            }
        } catch (e: Exception) {
            logger.error("读取根CA证书失败", e)
            null
        }
    }
    
    /**
     * 获取证书信息
     */
    fun getCertificateInfo(): Map<String, Any> {
        val info = mutableMapOf<String, Any>()
        
        try {
            val rootCertFile = File(certificatePath, "root-ca.crt")
            val serverCertFile = File(certificatePath, "server.crt")
            val keyStoreFile = File(keyStorePath)
            
            info["root_ca_exists"] = rootCertFile.exists()
            info["server_cert_exists"] = serverCertFile.exists()
            info["keystore_exists"] = keyStoreFile.exists()
            
            if (rootCertFile.exists()) {
                info["root_ca_path"] = rootCertFile.absolutePath
                info["root_ca_size"] = rootCertFile.length()
                info["root_ca_modified"] = Date(rootCertFile.lastModified())
            }
            
            if (serverCertFile.exists()) {
                info["server_cert_path"] = serverCertFile.absolutePath
                info["server_cert_size"] = serverCertFile.length()
                info["server_cert_modified"] = Date(serverCertFile.lastModified())
            }
            
            if (keyStoreFile.exists()) {
                info["keystore_path"] = keyStoreFile.absolutePath
                info["keystore_size"] = keyStoreFile.length()
                info["keystore_modified"] = Date(keyStoreFile.lastModified())
            }
            
        } catch (e: Exception) {
            logger.error("获取证书信息失败", e)
            info["error"] = e.message ?: "未知错误"
        }
        
        return info
    }
    
    /**
     * 清理证书文件
     */
    fun cleanup() {
        try {
            val certDir = File(certificatePath)
            if (certDir.exists()) {
                certDir.deleteRecursively()
                logger.info("已清理证书目录: ${certDir.absolutePath}")
            }
            
            val keyStoreFile = File(keyStorePath)
            if (keyStoreFile.exists()) {
                keyStoreFile.delete()
                logger.info("已删除密钥库: ${keyStoreFile.absolutePath}")
            }
        } catch (e: Exception) {
            logger.error("清理证书文件失败", e)
        }
    }
}
