package com.dev.tools.capture.security

import com.dev.tools.capture.exception.SslCertificateException
import org.bouncycastle.asn1.x500.X500Name
import org.bouncycastle.asn1.x509.BasicConstraints
import org.bouncycastle.asn1.x509.Extension
import org.bouncycastle.asn1.x509.GeneralName
import org.bouncycastle.asn1.x509.GeneralNames
import org.bouncycastle.cert.X509CertificateHolder
import org.bouncycastle.cert.X509v3CertificateBuilder
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter
import org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.slf4j.LoggerFactory
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.math.BigInteger
import java.nio.file.Files
import java.nio.file.Paths
import java.security.*
import java.security.cert.CertificateFactory
import java.security.cert.X509Certificate
import java.security.spec.PKCS8EncodedKeySpec
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*
import javax.net.ssl.KeyManager
import javax.net.ssl.KeyManagerFactory
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 证书提供者
 * 
 * 负责生成和管理SSL证书
 */
class CertificateProvider {
    
    private val logger = LoggerFactory.getLogger(CertificateProvider::class.java)
    
    // 根证书相关
    private var rootKeyPair: KeyPair? = null
    private var rootCertificate: X509Certificate? = null
    private val rootCertificateGenerated = AtomicBoolean(false)
    
    // 证书有效期（天）
    private val certificateValidityDays = 365L

    // 证书存储目录
    private val certificateDir = File("temp/ssl/certificates")
    private val hostCertificateDir = File(certificateDir, "hosts")

    // 私钥缓存 - 存储每个证书对应的私钥
    private val privateKeyCache = mutableMapOf<String, PrivateKey>()

    // 证书缓存 - 存储每个域名对应的证书
    private val certificateCache = mutableMapOf<String, X509Certificate>()
    
    init {
        // 添加BouncyCastle提供者
        if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null) {
            Security.addProvider(BouncyCastleProvider())
        }

        // 创建证书存储目录
        if (!certificateDir.exists()) {
            certificateDir.mkdirs()
            logger.debug("创建证书存储目录: ${certificateDir.absolutePath}")
        }
        if (!hostCertificateDir.exists()) {
            hostCertificateDir.mkdirs()
            logger.debug("创建主机证书存储目录: ${hostCertificateDir.absolutePath}")
        }
    }
    
    /**
     * 生成根证书
     */
    fun generateRootCertificate() {
        if (rootCertificateGenerated.get()) {
            logger.debug("根证书已存在，跳过生成")
            return
        }

        // 尝试从文件加载根证书
        if (loadRootCertificateFromFile()) {
            logger.info("从文件加载根证书成功")
            return
        }

        try {
            logger.info("开始生成新的根证书...")
            
            // 生成根证书密钥对
            val keyPairGenerator = KeyPairGenerator.getInstance("RSA")
            keyPairGenerator.initialize(2048)
            rootKeyPair = keyPairGenerator.generateKeyPair()
            
            // 创建根证书
            val now = Instant.now()
            val notBefore = Date.from(now)
            val notAfter = Date.from(now.plus(certificateValidityDays * 10, ChronoUnit.DAYS)) // 根证书有效期更长
            
            val issuer = X500Name("CN=PacketCapture Root CA, O=PacketCapture SDK, C=CN")
            val subject = issuer // 自签名
            val serialNumber = BigInteger.valueOf(System.currentTimeMillis())
            
            val certBuilder = JcaX509v3CertificateBuilder(
                issuer,
                serialNumber,
                notBefore,
                notAfter,
                subject,
                rootKeyPair!!.public
            )
            
            // 添加基本约束扩展（标记为CA证书）
            certBuilder.addExtension(
                Extension.basicConstraints,
                true,
                BasicConstraints(true)
            )
            
            // 签名并生成证书
            val contentSigner = JcaContentSignerBuilder("SHA256WithRSA")
                .setProvider("BC")
                .build(rootKeyPair!!.private)
            
            val certHolder = certBuilder.build(contentSigner)
            rootCertificate = JcaX509CertificateConverter()
                .setProvider("BC")
                .getCertificate(certHolder)
            
            rootCertificateGenerated.set(true)

            // 保存根证书到文件
            saveRootCertificateToFile()

            logger.info("根证书生成完成并已保存到文件")
            
        } catch (e: Exception) {
            logger.error("生成根证书失败", e)
            throw SslCertificateException.generationFailed("根证书生成失败", cause = e)
        }
    }
    
    /**
     * 为指定主机名生成服务器证书
     *
     * @param hostname 主机名
     * @return 服务器证书
     */
    fun generateServerCertificate(hostname: String): X509Certificate {
        if (!rootCertificateGenerated.get()) {
            throw SslCertificateException.generationFailed("根证书未生成")
        }

        // 检查缓存中是否已有该域名的证书
        certificateCache[hostname]?.let { cachedCert ->
            logger.debug("使用缓存的服务器证书: $hostname")
            return cachedCert
        }

        // 尝试从文件加载证书
        val loadedCert = loadServerCertificateFromFile(hostname)
        if (loadedCert != null) {
            logger.debug("从文件加载服务器证书: $hostname")
            return loadedCert
        }

        try {
            logger.debug("为主机生成新的服务器证书: $hostname")
            
            // 生成服务器证书密钥对
            val keyPairGenerator = KeyPairGenerator.getInstance("RSA")
            keyPairGenerator.initialize(2048)
            val serverKeyPair = keyPairGenerator.generateKeyPair()
            
            // 创建服务器证书
            val now = Instant.now()
            val notBefore = Date.from(now)
            val notAfter = Date.from(now.plus(certificateValidityDays, ChronoUnit.DAYS))
            
            val issuer = rootCertificate!!.subjectX500Principal.name
            val subject = X500Name("CN=$hostname, O=PacketCapture SDK, C=CN")
            val serialNumber = BigInteger.valueOf(System.currentTimeMillis() + hostname.hashCode())
            
            val certBuilder = JcaX509v3CertificateBuilder(
                X500Name(issuer),
                serialNumber,
                notBefore,
                notAfter,
                subject,
                serverKeyPair.public
            )
            
            // 添加主题备用名称扩展
            val sanBuilder = mutableListOf<GeneralName>()
            sanBuilder.add(GeneralName(GeneralName.dNSName, hostname))
            
            // 如果是IP地址，也添加IP SAN
            if (isIpAddress(hostname)) {
                sanBuilder.add(GeneralName(GeneralName.iPAddress, hostname))
            }
            
            // 添加通配符域名支持
            if (!hostname.startsWith("*.") && hostname.contains(".")) {
                val wildcardDomain = "*." + hostname.substringAfter(".")
                sanBuilder.add(GeneralName(GeneralName.dNSName, wildcardDomain))
            }
            
            if (sanBuilder.isNotEmpty()) {
                val subjectAltNames = GeneralNames(sanBuilder.toTypedArray())
                certBuilder.addExtension(
                    Extension.subjectAlternativeName,
                    false,
                    subjectAltNames
                )
            }
            
            // 签名并生成证书
            val contentSigner = JcaContentSignerBuilder("SHA256WithRSA")
                .setProvider("BC")
                .build(rootKeyPair!!.private)
            
            val certHolder = certBuilder.build(contentSigner)
            val serverCertificate = JcaX509CertificateConverter()
                .setProvider("BC")
                .getCertificate(certHolder)

            // 缓存私钥和证书，用于后续SSL握手
            privateKeyCache[hostname] = serverKeyPair.private
            certificateCache[hostname] = serverCertificate

            // 保存证书到文件
            saveServerCertificateToFile(hostname, serverCertificate, serverKeyPair.private)

            logger.debug("服务器证书生成完成并已保存: $hostname")

            // 调试：打印证书详细信息
            val certSubject = serverCertificate.subjectX500Principal.name
            val sanCollection = serverCertificate.subjectAlternativeNames
            val dnsNames = sanCollection
                ?.filter { it[0] == 2 } // DNS name type
                ?.map { it[1] as String }
                ?: emptyList()

            logger.error("🔍 生成的证书详情:")
            logger.error("   主机名: $hostname")
            logger.error("   证书主题: $certSubject")
            logger.error("   证书SAN: $dnsNames")
            logger.error("   证书有效期: ${serverCertificate.notBefore} - ${serverCertificate.notAfter}")
            logger.error("   证书指纹: ${java.security.MessageDigest.getInstance("SHA-256").digest(serverCertificate.encoded).joinToString("") { "%02x".format(it) }}")

            return serverCertificate
            
        } catch (e: Exception) {
            logger.error("生成服务器证书失败: $hostname", e)
            throw SslCertificateException.generationFailed("服务器证书生成失败: $hostname", cause = e)
        }
    }
    
    /**
     * 获取密钥管理器
     *
     * @param certificate 服务器证书
     * @param hostname 主机名，用于查找对应的私钥
     * @return 密钥管理器数组
     */
    fun getKeyManagers(certificate: X509Certificate, hostname: String): Array<KeyManager> {
        return try {
            // 调试：打印传入的证书信息
            val certSubject = certificate.subjectX500Principal.name
            val sanCollection = certificate.subjectAlternativeNames
            val dnsNames = sanCollection
                ?.filter { it[0] == 2 } // DNS name type
                ?.map { it[1] as String }
                ?: emptyList()

            logger.error("🔑 创建KeyManager:")
            logger.error("   主机名: $hostname")
            logger.error("   传入证书主题: $certSubject")
            logger.error("   传入证书SAN: $dnsNames")

            // 获取对应的私钥
            val privateKey = privateKeyCache[hostname]
                ?: throw SslCertificateException.generationFailed("未找到主机 $hostname 的私钥")

            // 创建密钥库
            val keyStore = KeyStore.getInstance("PKCS12")
            keyStore.load(null, null)

            // 将证书和私钥添加到密钥库（只使用服务器证书，不包含根证书）
            val certificateChain = arrayOf(certificate)
            keyStore.setKeyEntry(
                "server",
                privateKey, // 使用正确的服务器私钥
                "password".toCharArray(),
                certificateChain
            )

            // 创建密钥管理器
            val keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
            keyManagerFactory.init(keyStore, "password".toCharArray())

            keyManagerFactory.keyManagers

        } catch (e: Exception) {
            logger.error("创建密钥管理器失败: $hostname", e)
            throw SslCertificateException.generationFailed("密钥管理器创建失败: $hostname", cause = e)
        }
    }
    
    /**
     * 获取根证书的PEM格式
     * 
     * @return PEM格式的根证书
     */
    fun getRootCertificatePem(): String {
        if (!rootCertificateGenerated.get()) {
            throw SslCertificateException.generationFailed("根证书未生成")
        }
        
        return try {
            val encoded = Base64.getEncoder().encode(rootCertificate!!.encoded)
            val pemContent = String(encoded)
            
            buildString {
                appendLine("-----BEGIN CERTIFICATE-----")
                // 每64个字符换行
                pemContent.chunked(64).forEach { line ->
                    appendLine(line)
                }
                appendLine("-----END CERTIFICATE-----")
            }
            
        } catch (e: Exception) {
            logger.error("生成根证书PEM格式失败", e)
            throw SslCertificateException.generationFailed("PEM格式生成失败", cause = e)
        }
    }
    
    /**
     * 检查根证书是否已生成
     */
    fun isRootCertificateGenerated(): Boolean {
        return rootCertificateGenerated.get()
    }
    
    /**
     * 检查字符串是否为IP地址
     */
    private fun isIpAddress(hostname: String): Boolean {
        return try {
            val parts = hostname.split(".")
            if (parts.size != 4) return false
            
            parts.all { part ->
                val num = part.toIntOrNull()
                num != null && num in 0..255
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 清理指定域名的证书缓存
     *
     * @param hostname 主机名
     */
    fun clearCertificateCache(hostname: String) {
        certificateCache.remove(hostname)
        privateKeyCache.remove(hostname)
        logger.debug("已清理域名证书缓存: $hostname")
    }

    /**
     * 清理所有证书缓存
     */
    fun clearAllCertificateCache() {
        certificateCache.clear()
        privateKeyCache.clear()
        logger.debug("已清理所有证书缓存")
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        rootKeyPair = null
        rootCertificate = null
        rootCertificateGenerated.set(false)
        clearAllCertificateCache()
        logger.debug("证书提供者已清理")
    }

    /**
     * 从文件加载根证书
     */
    private fun loadRootCertificateFromFile(): Boolean {
        return try {
            val certFile = File(certificateDir, "root.crt")
            val keyFile = File(certificateDir, "root.key")

            if (!certFile.exists() || !keyFile.exists()) {
                logger.debug("根证书文件不存在")
                return false
            }

            // 加载证书
            val certFactory = CertificateFactory.getInstance("X.509")
            FileInputStream(certFile).use { fis ->
                rootCertificate = certFactory.generateCertificate(fis) as X509Certificate
            }

            // 加载私钥
            val keyBytes = Files.readAllBytes(keyFile.toPath())
            val keySpec = PKCS8EncodedKeySpec(keyBytes)
            val keyFactory = KeyFactory.getInstance("RSA")
            val privateKey = keyFactory.generatePrivate(keySpec)
            val publicKey = rootCertificate!!.publicKey
            rootKeyPair = KeyPair(publicKey, privateKey)

            rootCertificateGenerated.set(true)
            logger.debug("根证书从文件加载成功")
            true

        } catch (e: Exception) {
            logger.warn("从文件加载根证书失败", e)
            false
        }
    }

    /**
     * 保存根证书到文件
     */
    private fun saveRootCertificateToFile() {
        try {
            val certFile = File(certificateDir, "root.crt")
            val keyFile = File(certificateDir, "root.key")

            // 保存证书
            FileOutputStream(certFile).use { fos ->
                fos.write(rootCertificate!!.encoded)
            }

            // 保存私钥
            FileOutputStream(keyFile).use { fos ->
                fos.write(rootKeyPair!!.private.encoded)
            }

            logger.debug("根证书已保存到文件: ${certFile.absolutePath}")

        } catch (e: Exception) {
            logger.warn("保存根证书到文件失败", e)
        }
    }

    /**
     * 从文件加载服务器证书
     */
    private fun loadServerCertificateFromFile(hostname: String): X509Certificate? {
        return try {
            val hostDir = File(hostCertificateDir, hostname)
            val certFile = File(hostDir, "cert.crt")
            val keyFile = File(hostDir, "cert.key")

            if (!certFile.exists() || !keyFile.exists()) {
                logger.debug("服务器证书文件不存在: $hostname")
                return null
            }

            // 加载证书
            val certFactory = CertificateFactory.getInstance("X.509")
            val certificate = FileInputStream(certFile).use { fis ->
                certFactory.generateCertificate(fis) as X509Certificate
            }

            // 加载私钥
            val keyBytes = Files.readAllBytes(keyFile.toPath())
            val keySpec = PKCS8EncodedKeySpec(keyBytes)
            val keyFactory = KeyFactory.getInstance("RSA")
            val privateKey = keyFactory.generatePrivate(keySpec)

            // 缓存证书和私钥
            certificateCache[hostname] = certificate
            privateKeyCache[hostname] = privateKey

            logger.debug("服务器证书从文件加载成功: $hostname")
            certificate

        } catch (e: Exception) {
            logger.warn("从文件加载服务器证书失败: $hostname", e)
            null
        }
    }

    /**
     * 保存服务器证书到文件
     */
    private fun saveServerCertificateToFile(hostname: String, certificate: X509Certificate, privateKey: PrivateKey) {
        try {
            val hostDir = File(hostCertificateDir, hostname)
            if (!hostDir.exists()) {
                hostDir.mkdirs()
            }

            val certFile = File(hostDir, "cert.crt")
            val keyFile = File(hostDir, "cert.key")

            // 保存证书
            FileOutputStream(certFile).use { fos ->
                fos.write(certificate.encoded)
            }

            // 保存私钥
            FileOutputStream(keyFile).use { fos ->
                fos.write(privateKey.encoded)
            }

            logger.debug("服务器证书已保存到文件: ${hostDir.absolutePath}")

        } catch (e: Exception) {
            logger.warn("保存服务器证书到文件失败: $hostname", e)
        }
    }
}
