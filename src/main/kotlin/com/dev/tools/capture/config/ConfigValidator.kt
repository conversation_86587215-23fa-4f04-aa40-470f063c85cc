package com.dev.tools.capture.config

import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.SDKConfig

/**
 * 配置验证器
 */
object ConfigValidator {
    
    fun validate(config: SDKConfig): ValidationResult {
        val errors = mutableListOf<ValidationError>()
        val warnings = mutableListOf<ValidationWarning>()
        
        // 验证端口
        if (config.port < 1 || config.port > 65535) {
            errors.add(ValidationError("INVALID_PORT", "端口号必须在1-65535之间", "port", config.port))
        }
        
        // 验证协议
        if (config.protocols.isEmpty()) {
            errors.add(ValidationError("EMPTY_PROTOCOLS", "至少需要支持一种协议", "protocols"))
        }
        
        // 验证连接数
        if (config.maxConnections < 1) {
            errors.add(ValidationError("INVALID_MAX_CONNECTIONS", "最大连接数必须大于0", "maxConnections", config.maxConnections))
        }
        
        return ValidationResult(errors.isEmpty(), errors, warnings)
    }
}

data class ValidationResult(
    val isValid: Boolean,
    val errors: List<ValidationError>,
    val warnings: List<ValidationWarning>
) {
    val hasWarnings: Boolean get() = warnings.isNotEmpty()
    
    fun getAllMessages(): List<String> {
        return errors.map { it.message } + warnings.map { it.message }
    }
}

data class ValidationError(
    val code: String,
    val message: String,
    val field: String? = null,
    val value: Any? = null
)

data class ValidationWarning(
    val code: String,
    val message: String,
    val field: String? = null,
    val value: Any? = null
)
