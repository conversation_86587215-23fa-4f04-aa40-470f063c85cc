package com.dev.tools.capture.config

import com.dev.tools.capture.model.*

/**
 * 配置模板
 * 
 * 提供预定义的配置模板，适用于不同的使用场景
 */
object ConfigTemplates {
    
    /**
     * 开发环境配置
     */
    fun development(): SDKConfig {
        return SDKConfig(
            port = 8888,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET, Protocol.WSS),
            enableSslMitm = true,
            sslFallbackStrategy = FallbackStrategyType.LOG_AND_CONTINUE,
            storageConfig = StorageConfig.MEMORY,
            maxConnections = 100,
            connectionTimeout = 60000, // 1分钟
            readTimeout = 120000, // 2分钟
            writeTimeout = 120000, // 2分钟
            bufferSize = 8192,
            enableLogging = true,
            logLevel = LogLevel.DEBUG,
            enableMetrics = true,
            enableDataMasking = false,
            customAttributes = mapOf(
                "environment" to "development",
                "debug_mode" to "true"
            )
        )
    }
    
    /**
     * 测试环境配置
     */
    fun testing(): SDKConfig {
        return SDKConfig(
            port = 9999,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET, Protocol.MQTT),
            enableSslMitm = true,
            sslFallbackStrategy = FallbackStrategyType.TUNNEL,
            storageConfig = StorageConfig.FILE,
            maxConnections = 500,
            connectionTimeout = 30000,
            readTimeout = 60000,
            writeTimeout = 60000,
            bufferSize = 16384,
            enableLogging = true,
            logLevel = LogLevel.INFO,
            enableMetrics = true,
            enableDataMasking = false,
            customAttributes = mapOf(
                "environment" to "testing",
                "test_mode" to "true"
            )
        )
    }
    
    /**
     * 生产环境配置
     */
    fun production(): SDKConfig {
        return SDKConfig(
            port = 8080,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET, Protocol.WSS, Protocol.MQTT, Protocol.MQTTS),
            enableSslMitm = true,
            sslFallbackStrategy = FallbackStrategyType.TUNNEL,
            storageConfig = StorageConfig.DATABASE,
            maxConnections = 10000,
            connectionTimeout = 30000,
            readTimeout = 60000,
            writeTimeout = 60000,
            bufferSize = 32768,
            enableLogging = true,
            logLevel = LogLevel.WARN,
            enableMetrics = true,
            enableDataMasking = true,
            customAttributes = mapOf(
                "environment" to "production",
                "security_level" to "high"
            )
        )
    }
    
    /**
     * 高性能配置
     */
    fun highPerformance(): SDKConfig {
        return SDKConfig(
            port = 8080,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET, Protocol.WSS),
            enableSslMitm = false, // 禁用SSL解密以提高性能
            sslFallbackStrategy = FallbackStrategyType.TUNNEL,
            storageConfig = StorageConfig.MEMORY,
            maxConnections = 50000,
            connectionTimeout = 10000, // 较短的超时
            readTimeout = 30000,
            writeTimeout = 30000,
            bufferSize = 65536, // 更大的缓冲区
            enableLogging = false, // 禁用日志以提高性能
            logLevel = LogLevel.ERROR,
            enableMetrics = false, // 禁用指标收集
            enableDataMasking = false,
            customAttributes = mapOf(
                "performance_mode" to "high",
                "optimization" to "speed"
            )
        )
    }
    
    /**
     * 安全优先配置
     */
    fun securityFocused(): SDKConfig {
        return SDKConfig(
            port = 8443, // 使用HTTPS端口
            protocols = setOf(Protocol.HTTPS, Protocol.WSS, Protocol.MQTTS), // 只支持安全协议
            enableSslMitm = true,
            sslFallbackStrategy = FallbackStrategyType.REJECT, // 拒绝不安全连接
            storageConfig = StorageConfig.DATABASE,
            maxConnections = 1000, // 限制连接数
            connectionTimeout = 15000, // 较短的超时
            readTimeout = 30000,
            writeTimeout = 30000,
            bufferSize = 8192,
            enableLogging = true,
            logLevel = LogLevel.INFO,
            enableMetrics = true,
            enableDataMasking = true, // 启用数据脱敏
            customAttributes = mapOf(
                "security_level" to "high",
                "audit_mode" to "true",
                "encryption_required" to "true"
            )
        )
    }
    
    /**
     * 调试配置
     */
    fun debugging(): SDKConfig {
        return SDKConfig(
            port = 8888,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET, Protocol.WSS, Protocol.MQTT, Protocol.MQTTS),
            enableSslMitm = true,
            sslFallbackStrategy = FallbackStrategyType.LOG_AND_CONTINUE,
            storageConfig = StorageConfig.FILE,
            maxConnections = 50,
            connectionTimeout = 300000, // 5分钟，便于调试
            readTimeout = 600000, // 10分钟
            writeTimeout = 600000, // 10分钟
            bufferSize = 4096,
            enableLogging = true,
            logLevel = LogLevel.TRACE, // 最详细的日志
            enableMetrics = true,
            enableDataMasking = false, // 禁用脱敏以便调试
            customAttributes = mapOf(
                "debug_mode" to "true",
                "verbose_logging" to "true",
                "trace_enabled" to "true"
            )
        )
    }
    
    /**
     * 最小配置
     */
    fun minimal(): SDKConfig {
        return SDKConfig(
            port = 8080,
            protocols = setOf(Protocol.HTTP),
            enableSslMitm = false,
            sslFallbackStrategy = FallbackStrategyType.REJECT,
            storageConfig = StorageConfig.MEMORY,
            maxConnections = 10,
            connectionTimeout = 30000,
            readTimeout = 60000,
            writeTimeout = 60000,
            bufferSize = 1024,
            enableLogging = false,
            logLevel = LogLevel.ERROR,
            enableMetrics = false,
            enableDataMasking = false,
            customAttributes = emptyMap()
        )
    }
    
    /**
     * 移动设备配置
     */
    fun mobile(): SDKConfig {
        return SDKConfig(
            port = 8080,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET),
            enableSslMitm = true,
            sslFallbackStrategy = FallbackStrategyType.TUNNEL,
            storageConfig = StorageConfig.FILE,
            maxConnections = 100, // 移动设备连接数较少
            connectionTimeout = 20000, // 较短超时适应移动网络
            readTimeout = 40000,
            writeTimeout = 40000,
            bufferSize = 4096, // 较小缓冲区节省内存
            enableLogging = true,
            logLevel = LogLevel.WARN,
            enableMetrics = false, // 节省资源
            enableDataMasking = true,
            customAttributes = mapOf(
                "platform" to "mobile",
                "memory_optimized" to "true",
                "battery_aware" to "true"
            )
        )
    }
    
    /**
     * 获取所有模板
     */
    fun getAllTemplates(): Map<String, SDKConfig> {
        return mapOf(
            "development" to development(),
            "testing" to testing(),
            "production" to production(),
            "high_performance" to highPerformance(),
            "security_focused" to securityFocused(),
            "debugging" to debugging(),
            "minimal" to minimal(),
            "mobile" to mobile()
        )
    }
    
    /**
     * 根据名称获取模板
     */
    fun getTemplate(name: String): SDKConfig? {
        return when (name.lowercase()) {
            "development", "dev" -> development()
            "testing", "test" -> testing()
            "production", "prod" -> production()
            "high_performance", "performance", "perf" -> highPerformance()
            "security_focused", "security", "secure" -> securityFocused()
            "debugging", "debug" -> debugging()
            "minimal", "min" -> minimal()
            "mobile" -> mobile()
            else -> null
        }
    }
    
    /**
     * 获取模板描述
     */
    fun getTemplateDescription(name: String): String {
        return when (name.lowercase()) {
            "development", "dev" -> "开发环境配置，启用详细日志和调试功能"
            "testing", "test" -> "测试环境配置，平衡功能和性能"
            "production", "prod" -> "生产环境配置，注重稳定性和安全性"
            "high_performance", "performance", "perf" -> "高性能配置，优化速度和吞吐量"
            "security_focused", "security", "secure" -> "安全优先配置，只支持加密协议"
            "debugging", "debug" -> "调试配置，启用详细跟踪和长超时"
            "minimal", "min" -> "最小配置，仅包含基本功能"
            "mobile" -> "移动设备配置，优化内存和电池使用"
            else -> "未知模板"
        }
    }
}
