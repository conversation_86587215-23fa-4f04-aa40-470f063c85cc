package com.dev.tools.capture.config

import com.dev.tools.capture.exception.ConfigException
import com.dev.tools.capture.model.ConfigPatch
import com.dev.tools.capture.model.SDKConfig
import kotlinx.serialization.json.Json
import org.slf4j.LoggerFactory
import java.io.File
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicReference

/**
 * 配置管理器
 * 
 * 负责配置的加载、保存、验证和动态更新
 */
class ConfigManager {
    
    private val logger = LoggerFactory.getLogger(ConfigManager::class.java)
    
    // 当前配置
    private val currentConfig = AtomicReference<SDKConfig>()
    
    // 配置变更监听器
    private val configListeners = CopyOnWriteArrayList<ConfigChangeListener>()
    
    // JSON序列化器
    private val json = Json {
        prettyPrint = true
        ignoreUnknownKeys = true
    }
    
    /**
     * 初始化配置管理器
     * 
     * @param initialConfig 初始配置
     */
    fun initialize(initialConfig: SDKConfig) {
        // 验证初始配置
        val validationResult = ConfigValidator.validate(initialConfig)
        if (!validationResult.isValid) {
            throw ConfigException.validationFailed(validationResult.errors.map { it.message })
        }
        
        currentConfig.set(initialConfig)
        logger.info("配置管理器初始化完成")
        
        // 记录警告
        if (validationResult.hasWarnings) {
            validationResult.warnings.forEach { warning ->
                logger.warn("配置警告: ${warning.message}")
            }
        }
    }
    
    /**
     * 获取当前配置
     * 
     * @return 当前配置的副本
     */
    fun getCurrentConfig(): SDKConfig {
        return currentConfig.get()?.copy() ?: throw IllegalStateException("配置管理器未初始化")
    }
    
    /**
     * 更新配置
     * 
     * @param configPatch 配置更新补丁
     * @throws ConfigException 如果配置无效
     */
    @Throws(ConfigException::class)
    fun updateConfig(configPatch: ConfigPatch) {
        val oldConfig = getCurrentConfig()
        val newConfig = oldConfig.update(configPatch)
        
        // 验证新配置
        val validationResult = ConfigValidator.validate(newConfig)
        if (!validationResult.isValid) {
            throw ConfigException.validationFailed(validationResult.errors.map { it.message })
        }
        
        // 应用配置
        val previousConfig = currentConfig.getAndSet(newConfig)
        
        logger.info("配置已更新")
        
        // 记录警告
        if (validationResult.hasWarnings) {
            validationResult.warnings.forEach { warning ->
                logger.warn("配置警告: ${warning.message}")
            }
        }
        
        // 通知监听器
        notifyConfigChanged(previousConfig, newConfig, configPatch)
    }
    
    /**
     * 从文件加载配置
     * 
     * @param configFile 配置文件
     * @return 加载的配置
     * @throws ConfigException 如果加载失败
     */
    @Throws(ConfigException::class)
    fun loadFromFile(configFile: File): SDKConfig {
        return try {
            if (!configFile.exists()) {
                throw ConfigException.missingRequired("配置文件不存在: ${configFile.absolutePath}")
            }
            
            val configText = configFile.readText()
            val config = json.decodeFromString<SDKConfig>(configText)
            
            // 验证配置
            val validationResult = ConfigValidator.validate(config)
            if (!validationResult.isValid) {
                throw ConfigException.validationFailed(validationResult.errors.map { it.message })
            }
            
            logger.info("从文件加载配置成功: ${configFile.absolutePath}")
            
            // 记录警告
            if (validationResult.hasWarnings) {
                validationResult.warnings.forEach { warning ->
                    logger.warn("配置警告: ${warning.message}")
                }
            }
            
            config
            
        } catch (e: Exception) {
            logger.error("从文件加载配置失败: ${configFile.absolutePath}", e)
            throw ConfigException.invalidValue("configFile", configFile.absolutePath, e.message ?: "加载失败")
        }
    }
    
    /**
     * 保存配置到文件
     * 
     * @param configFile 配置文件
     * @param config 要保存的配置（可选，默认使用当前配置）
     * @throws ConfigException 如果保存失败
     */
    @Throws(ConfigException::class)
    fun saveToFile(configFile: File, config: SDKConfig? = null) {
        val configToSave = config ?: getCurrentConfig()
        
        try {
            // 确保父目录存在
            configFile.parentFile?.mkdirs()
            
            val configText = json.encodeToString(SDKConfig.serializer(), configToSave)
            configFile.writeText(configText)
            
            logger.info("配置保存成功: ${configFile.absolutePath}")
            
        } catch (e: Exception) {
            logger.error("保存配置失败: ${configFile.absolutePath}", e)
            throw ConfigException.invalidValue("configFile", configFile.absolutePath, e.message ?: "保存失败")
        }
    }
    
    /**
     * 验证配置
     * 
     * @param config 要验证的配置
     * @return 验证结果
     */
    fun validateConfig(config: SDKConfig): ValidationResult {
        return ConfigValidator.validate(config)
    }
    
    /**
     * 注册配置变更监听器
     * 
     * @param listener 监听器
     */
    fun addConfigChangeListener(listener: ConfigChangeListener) {
        configListeners.add(listener)
        logger.debug("注册配置变更监听器: ${listener::class.simpleName}")
    }
    
    /**
     * 注销配置变更监听器
     * 
     * @param listener 监听器
     * @return 是否成功注销
     */
    fun removeConfigChangeListener(listener: ConfigChangeListener): Boolean {
        val removed = configListeners.remove(listener)
        if (removed) {
            logger.debug("注销配置变更监听器: ${listener::class.simpleName}")
        }
        return removed
    }
    
    /**
     * 通知配置变更
     */
    private fun notifyConfigChanged(
        oldConfig: SDKConfig,
        newConfig: SDKConfig,
        configPatch: ConfigPatch
    ) {
        if (configListeners.isEmpty()) {
            return
        }
        
        val changeEvent = ConfigChangeEvent(oldConfig, newConfig, configPatch)
        
        configListeners.forEach { listener ->
            try {
                listener.onConfigChanged(changeEvent)
            } catch (e: Exception) {
                logger.error("配置变更监听器执行失败: ${listener::class.simpleName}", e)
            }
        }
    }
    
    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        val defaultConfig = SDKConfig()
        currentConfig.set(defaultConfig)
        logger.info("配置已重置为默认值")
        
        // 通知监听器
        notifyConfigChanged(getCurrentConfig(), defaultConfig, ConfigPatch())
    }
    
    /**
     * 获取配置差异
     * 
     * @param config1 配置1
     * @param config2 配置2
     * @return 配置差异列表
     */
    fun getConfigDifferences(config1: SDKConfig, config2: SDKConfig): List<ConfigDifference> {
        val differences = mutableListOf<ConfigDifference>()
        
        if (config1.port != config2.port) {
            differences.add(ConfigDifference("port", config1.port, config2.port))
        }
        
        if (config1.protocols != config2.protocols) {
            differences.add(ConfigDifference("protocols", config1.protocols, config2.protocols))
        }
        
        if (config1.enableSslMitm != config2.enableSslMitm) {
            differences.add(ConfigDifference("enableSslMitm", config1.enableSslMitm, config2.enableSslMitm))
        }
        
        if (config1.sslFallbackStrategy != config2.sslFallbackStrategy) {
            differences.add(ConfigDifference("sslFallbackStrategy", config1.sslFallbackStrategy, config2.sslFallbackStrategy))
        }
        
        if (config1.storageConfig != config2.storageConfig) {
            differences.add(ConfigDifference("storageConfig", config1.storageConfig, config2.storageConfig))
        }
        
        if (config1.maxConnections != config2.maxConnections) {
            differences.add(ConfigDifference("maxConnections", config1.maxConnections, config2.maxConnections))
        }
        
        if (config1.connectionTimeout != config2.connectionTimeout) {
            differences.add(ConfigDifference("connectionTimeout", config1.connectionTimeout, config2.connectionTimeout))
        }
        
        if (config1.readTimeout != config2.readTimeout) {
            differences.add(ConfigDifference("readTimeout", config1.readTimeout, config2.readTimeout))
        }
        
        if (config1.writeTimeout != config2.writeTimeout) {
            differences.add(ConfigDifference("writeTimeout", config1.writeTimeout, config2.writeTimeout))
        }
        
        if (config1.bufferSize != config2.bufferSize) {
            differences.add(ConfigDifference("bufferSize", config1.bufferSize, config2.bufferSize))
        }
        
        if (config1.enableLogging != config2.enableLogging) {
            differences.add(ConfigDifference("enableLogging", config1.enableLogging, config2.enableLogging))
        }
        
        if (config1.logLevel != config2.logLevel) {
            differences.add(ConfigDifference("logLevel", config1.logLevel, config2.logLevel))
        }
        
        if (config1.enableMetrics != config2.enableMetrics) {
            differences.add(ConfigDifference("enableMetrics", config1.enableMetrics, config2.enableMetrics))
        }
        
        if (config1.enableDataMasking != config2.enableDataMasking) {
            differences.add(ConfigDifference("enableDataMasking", config1.enableDataMasking, config2.enableDataMasking))
        }
        
        if (config1.customAttributes != config2.customAttributes) {
            differences.add(ConfigDifference("customAttributes", config1.customAttributes, config2.customAttributes))
        }
        
        return differences
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        configListeners.clear()
        logger.info("配置管理器已清理")
    }
}

/**
 * 配置变更监听器接口
 */
interface ConfigChangeListener {
    /**
     * 配置变更时调用
     * 
     * @param event 变更事件
     */
    fun onConfigChanged(event: ConfigChangeEvent)
}

/**
 * 配置变更事件
 */
data class ConfigChangeEvent(
    val oldConfig: SDKConfig,
    val newConfig: SDKConfig,
    val configPatch: ConfigPatch
)

/**
 * 配置差异
 */
data class ConfigDifference(
    val field: String,
    val oldValue: Any?,
    val newValue: Any?
) {
    override fun toString(): String {
        return "$field: $oldValue -> $newValue"
    }
}
