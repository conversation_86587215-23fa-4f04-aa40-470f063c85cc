package com.dev.tools.capture

import com.dev.tools.capture.api.PacketCallback
import com.dev.tools.capture.callback.CallbackManager
import com.dev.tools.capture.core.PacketCaptureEngine
import com.dev.tools.capture.exception.ConfigException
import com.dev.tools.capture.mock.MockEngine
import com.dev.tools.capture.model.ConfigPatch
import com.dev.tools.capture.model.MockRule
import com.dev.tools.capture.model.SDKConfig
import kotlinx.coroutines.*
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 抓包SDK主入口类
 * 
 * 提供网络流量抓取、分析和Mock功能的统一接口
 * 
 * @property config SDK配置
 */
class PacketCaptureSDK(private var config: SDKConfig) {
    
    private val logger = LoggerFactory.getLogger(PacketCaptureSDK::class.java)
    
    // 运行状态
    private val isRunning = AtomicBoolean(false)
    private val isInitialized = AtomicBoolean(false)
    
    // 核心组件
    private val callbackManager = CallbackManager()
    private val mockEngine = MockEngine()
    private val captureEngineRef = AtomicReference<PacketCaptureEngine?>()
    
    // 协程作用域
    private var sdkScope: CoroutineScope? = null
    
    init {
        validateConfig()
        logger.info("PacketCaptureSDK 初始化完成，端口: ${config.port}")
    }
    
    /**
     * 启动SDK
     * 
     * @throws IllegalStateException 如果SDK已经在运行
     * @throws ConfigException 如果配置无效
     */
    @Throws(IllegalStateException::class, ConfigException::class)
    fun start() {
        if (isRunning.get()) {
            throw IllegalStateException("SDK已经在运行中")
        }
        
        validateConfig()
        
        logger.info("启动PacketCaptureSDK...")
        
        try {
            // 创建协程作用域
            sdkScope = CoroutineScope(
                SupervisorJob() + 
                Dispatchers.IO + 
                CoroutineName("PacketCaptureSDK")
            )
            
            // 创建并启动抓包引擎
            val engine = PacketCaptureEngine(
                config = config,
                callbackManager = callbackManager,
                mockEngine = mockEngine
            )
            
            captureEngineRef.set(engine)
            
            // 在协程中启动引擎
            sdkScope?.launch {
                try {
                    engine.start()
                    isInitialized.set(true)
                    isRunning.set(true)
                    logger.info("PacketCaptureSDK 启动成功，监听端口: ${config.port}")
                } catch (e: Exception) {
                    logger.error("PacketCaptureSDK 启动失败", e)
                    stop()
                    throw e
                }
            }
            
            // 等待初始化完成
            runBlocking {
                while (!isInitialized.get() && !isRunning.get()) {
                    delay(100)
                }
            }
            
        } catch (e: Exception) {
            logger.error("启动PacketCaptureSDK时发生错误", e)
            stop()
            throw e
        }
    }
    
    /**
     * 停止SDK
     */
    fun stop() {
        if (!isRunning.get()) {
            logger.warn("SDK未在运行中")
            return
        }
        
        logger.info("停止PacketCaptureSDK...")
        
        try {
            // 停止抓包引擎
            captureEngineRef.get()?.stop()
            captureEngineRef.set(null)
            
            // 取消协程作用域
            sdkScope?.cancel()
            sdkScope = null
            
            // 清理状态
            isRunning.set(false)
            isInitialized.set(false)
            
            logger.info("PacketCaptureSDK 已停止")
            
        } catch (e: Exception) {
            logger.error("停止PacketCaptureSDK时发生错误", e)
        }
    }
    
    /**
     * 注册流量回调
     * 
     * @param callback 回调实例
     * @return 回调ID，用于后续注销
     */
    fun registerCallback(callback: PacketCallback): String {
        val callbackId = callbackManager.register(callback)
        logger.debug("注册回调: $callbackId")
        return callbackId
    }
    
    /**
     * 注销流量回调
     * 
     * @param callback 回调实例
     * @return 是否成功注销
     */
    fun unregisterCallback(callback: PacketCallback): Boolean {
        val result = callbackManager.unregister(callback)
        logger.debug("注销回调: $result")
        return result
    }
    
    /**
     * 根据ID注销流量回调
     * 
     * @param callbackId 回调ID
     * @return 是否成功注销
     */
    fun unregisterCallback(callbackId: String): Boolean {
        val result = callbackManager.unregister(callbackId)
        logger.debug("注销回调ID: $callbackId, 结果: $result")
        return result
    }
    
    /**
     * 添加Mock规则
     * 
     * @param rule Mock规则
     * @throws IllegalArgumentException 如果规则无效
     */
    @Throws(IllegalArgumentException::class)
    fun addMockRule(rule: MockRule) {
        mockEngine.addRule(rule)
        logger.info("添加Mock规则: ${rule.name} (${rule.id})")
    }
    
    /**
     * 移除Mock规则
     * 
     * @param ruleId 规则ID
     * @return 是否成功移除
     */
    fun removeMockRule(ruleId: String): Boolean {
        val result = mockEngine.removeRule(ruleId)
        logger.info("移除Mock规则: $ruleId, 结果: $result")
        return result
    }
    
    /**
     * 获取所有Mock规则
     * 
     * @return Mock规则列表
     */
    fun getMockRules(): List<MockRule> {
        return mockEngine.getAllRules()
    }
    
    /**
     * 启用/禁用Mock规则
     * 
     * @param ruleId 规则ID
     * @param enabled 是否启用
     * @return 是否成功更新
     */
    fun setMockRuleEnabled(ruleId: String, enabled: Boolean): Boolean {
        val result = mockEngine.setRuleEnabled(ruleId, enabled)
        logger.info("设置Mock规则状态: $ruleId, 启用: $enabled, 结果: $result")
        return result
    }
    
    /**
     * 更新配置
     * 
     * @param configPatch 配置更新补丁
     * @throws ConfigException 如果配置无效
     * @throws IllegalStateException 如果SDK正在运行
     */
    @Throws(ConfigException::class, IllegalStateException::class)
    fun updateConfig(configPatch: ConfigPatch) {
        if (isRunning.get()) {
            throw IllegalStateException("无法在运行时更新配置，请先停止SDK")
        }
        
        val newConfig = config.update(configPatch)
        val errors = newConfig.validate()
        
        if (errors.isNotEmpty()) {
            throw ConfigException.validationFailed(errors)
        }
        
        config = newConfig
        logger.info("配置已更新")
    }
    
    /**
     * 获取当前配置
     * 
     * @return 当前配置的副本
     */
    fun getConfig(): SDKConfig {
        return config.copy()
    }
    
    /**
     * 检查SDK是否正在运行
     * 
     * @return 是否正在运行
     */
    fun isRunning(): Boolean {
        return isRunning.get()
    }
    
    /**
     * 获取运行状态信息
     * 
     * @return 状态信息映射
     */
    fun getStatus(): Map<String, Any> {
        val engine = captureEngineRef.get()
        
        return mapOf(
            "running" to isRunning.get(),
            "initialized" to isInitialized.get(),
            "port" to config.port,
            "protocols" to config.protocols.map { it.name },
            "callbackCount" to callbackManager.getCallbackCount(),
            "mockRuleCount" to mockEngine.getRuleCount(),
            "activeConnections" to (engine?.getActiveConnectionCount() ?: 0),
            "totalPacketsProcessed" to (engine?.getTotalPacketsProcessed() ?: 0)
        )
    }
    
    /**
     * 验证配置
     */
    private fun validateConfig() {
        val errors = config.validate()
        if (errors.isNotEmpty()) {
            throw ConfigException.validationFailed(errors)
        }
    }
}
