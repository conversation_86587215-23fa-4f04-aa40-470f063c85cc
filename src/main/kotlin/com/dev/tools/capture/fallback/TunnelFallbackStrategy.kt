package com.dev.tools.capture.fallback

import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.exception.SslDecryptionException
import com.dev.tools.capture.exception.SslHandshakeException
import com.dev.tools.capture.model.FallbackStrategyType
import com.dev.tools.capture.model.ProtocolContext
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicLong

/**
 * 隧道模式降级策略
 * 
 * 当SSL解密失败时，切换到隧道模式直接转发加密数据
 */
class TunnelFallbackStrategy : FallbackStrategy {
    
    private val logger = LoggerFactory.getLogger(TunnelFallbackStrategy::class.java)
    
    override val type: FallbackStrategyType = FallbackStrategyType.TUNNEL
    override val name: String = "隧道模式降级策略"
    override val description: String = "SSL解密失败时切换到隧道模式，直接转发加密数据"
    
    // 统计信息
    private val totalExecutions = AtomicLong(0)
    private val successfulExecutions = AtomicLong(0)
    private val failedExecutions = AtomicLong(0)
    private val sslFailureCount = AtomicLong(0)
    private val tunnelConnectionCount = AtomicLong(0)
    
    override fun canHandle(exception: CaptureException, context: ProtocolContext): Boolean {
        // 只处理SSL相关的异常
        return when (exception) {
            is SslDecryptionException,
            is SslHandshakeException -> {
                // 确保协议支持SSL
                context.protocol.isSecure
            }
            else -> false
        }
    }
    
    override fun execute(
        ctx: ChannelHandlerContext,
        exception: CaptureException,
        context: ProtocolContext
    ): FallbackResult {
        totalExecutions.incrementAndGet()
        
        try {
            logger.info("执行隧道模式降级策略: 会话=${context.session.id}, 异常=${exception.errorCode}")
            
            when (exception) {
                is SslDecryptionException,
                is SslHandshakeException -> {
                    return handleSslFailure(ctx, exception, context)
                }
                else -> {
                    logger.warn("不支持的异常类型: ${exception::class.simpleName}")
                    failedExecutions.incrementAndGet()
                    return FallbackResult.failure(
                        "不支持的异常类型: ${exception::class.simpleName}",
                        mapOf("exception_type" to exception::class.simpleName!!)
                    )
                }
            }
            
        } catch (e: Exception) {
            logger.error("执行隧道模式降级策略失败", e)
            failedExecutions.incrementAndGet()
            return FallbackResult.failure(
                "降级策略执行失败: ${e.message}",
                mapOf("error" to e.message!!)
            )
        }
    }
    
    /**
     * 处理SSL失败
     */
    private fun handleSslFailure(
        ctx: ChannelHandlerContext,
        exception: CaptureException,
        context: ProtocolContext
    ): FallbackResult {
        sslFailureCount.incrementAndGet()
        
        logger.debug("SSL处理失败，切换到隧道模式: ${exception.message}")
        
        try {
            // 设置隧道模式标记
            ctx.channel().attr(TUNNEL_MODE_KEY).set(true)
            
            // 记录隧道连接
            tunnelConnectionCount.incrementAndGet()
            
            // 移除SSL处理器（如果存在）
            removeSslHandlers(ctx)
            
            // 添加隧道处理器
            addTunnelHandler(ctx, context)
            
            successfulExecutions.incrementAndGet()
            
            logger.info("成功切换到隧道模式: 会话=${context.session.id}")
            
            return FallbackResult.success(
                FallbackAction.SWITCH_TO_TUNNEL,
                "已切换到隧道模式",
                mapOf(
                    "tunnel_mode" to true,
                    "original_protocol" to context.protocol.name,
                    "session_id" to context.session.id
                )
            )
            
        } catch (e: Exception) {
            logger.error("切换到隧道模式失败", e)
            failedExecutions.incrementAndGet()
            
            return FallbackResult.failure(
                "切换到隧道模式失败: ${e.message}",
                mapOf("error" to e.message!!)
            )
        }
    }
    
    /**
     * 移除SSL处理器
     */
    private fun removeSslHandlers(ctx: ChannelHandlerContext) {
        val pipeline = ctx.pipeline()
        
        // 移除常见的SSL处理器
        val sslHandlerNames = listOf("ssl", "sslHandler", "sslEngine")
        
        for (handlerName in sslHandlerNames) {
            try {
                if (pipeline.get(handlerName) != null) {
                    pipeline.remove(handlerName)
                    logger.debug("移除SSL处理器: $handlerName")
                }
            } catch (e: Exception) {
                logger.debug("移除SSL处理器失败: $handlerName", e)
            }
        }
    }
    
    /**
     * 添加隧道处理器
     */
    private fun addTunnelHandler(ctx: ChannelHandlerContext, context: ProtocolContext) {
        val pipeline = ctx.pipeline()
        
        // 添加简单的数据转发处理器
        val tunnelHandler = TunnelHandler(context)
        
        try {
            pipeline.addLast("tunnelHandler", tunnelHandler)
            logger.debug("添加隧道处理器成功")
        } catch (e: Exception) {
            logger.warn("添加隧道处理器失败", e)
            throw e
        }
    }
    
    override fun getStatistics(): Map<String, Any> {
        val total = totalExecutions.get()
        val successful = successfulExecutions.get()
        val failed = failedExecutions.get()
        
        return mapOf(
            "strategy_name" to name,
            "strategy_type" to type.name,
            "total_executions" to total,
            "successful_executions" to successful,
            "failed_executions" to failed,
            "success_rate" to if (total > 0) successful.toDouble() / total else 0.0,
            "ssl_failure_count" to sslFailureCount.get(),
            "tunnel_connection_count" to tunnelConnectionCount.get()
        )
    }
    
    override fun resetStatistics() {
        totalExecutions.set(0)
        successfulExecutions.set(0)
        failedExecutions.set(0)
        sslFailureCount.set(0)
        tunnelConnectionCount.set(0)
        logger.info("隧道模式降级策略统计信息已重置")
    }
    
    companion object {
        /**
         * 隧道模式标记键
         */
        private val TUNNEL_MODE_KEY = io.netty.util.AttributeKey.valueOf<Boolean>("tunnel_mode")
        
        /**
         * 检查连接是否处于隧道模式
         */
        fun isTunnelMode(ctx: ChannelHandlerContext): Boolean {
            return ctx.channel().attr(TUNNEL_MODE_KEY).get() ?: false
        }
    }
}
