package com.dev.tools.capture.fallback

import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.exception.FallbackException
import com.dev.tools.capture.model.FallbackStrategyType
import com.dev.tools.capture.model.ProtocolContext
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 降级策略管理器
 * 
 * 负责管理和执行各种降级策略
 */
class FallbackManager {
    
    private val logger = LoggerFactory.getLogger(FallbackManager::class.java)
    
    // 策略映射：策略类型 -> 策略实例
    private val strategies = ConcurrentHashMap<FallbackStrategyType, FallbackStrategy>()
    
    // 默认策略
    private var defaultStrategy: FallbackStrategy? = null
    
    // 统计信息
    private val totalExecutions = AtomicLong(0)
    private val successfulExecutions = AtomicLong(0)
    private val failedExecutions = AtomicLong(0)
    
    init {
        // 注册默认策略
        registerDefaultStrategies()
    }
    
    /**
     * 注册默认策略
     */
    private fun registerDefaultStrategies() {
        registerStrategy(TunnelFallbackStrategy())
        registerStrategy(RejectFallbackStrategy())
        registerStrategy(LogAndContinueFallbackStrategy())
        
        // 设置隧道模式为默认策略
        defaultStrategy = strategies[FallbackStrategyType.TUNNEL]
        
        logger.info("已注册默认降级策略: ${strategies.keys.map { it.name }}")
    }
    
    /**
     * 注册降级策略
     * 
     * @param strategy 策略实例
     */
    fun registerStrategy(strategy: FallbackStrategy) {
        val existingStrategy = strategies.put(strategy.type, strategy)
        if (existingStrategy != null) {
            logger.warn("降级策略被替换: ${strategy.type} - ${existingStrategy.name} -> ${strategy.name}")
        } else {
            logger.info("注册降级策略: ${strategy.type} - ${strategy.name}")
        }
    }
    
    /**
     * 注销降级策略
     * 
     * @param strategyType 策略类型
     * @return 是否成功注销
     */
    fun unregisterStrategy(strategyType: FallbackStrategyType): Boolean {
        val strategy = strategies.remove(strategyType)
        return if (strategy != null) {
            logger.info("注销降级策略: ${strategyType} - ${strategy.name}")
            
            // 如果注销的是默认策略，清除默认策略
            if (defaultStrategy == strategy) {
                defaultStrategy = null
            }
            
            true
        } else {
            false
        }
    }
    
    /**
     * 设置默认策略
     * 
     * @param strategyType 策略类型
     * @return 是否成功设置
     */
    fun setDefaultStrategy(strategyType: FallbackStrategyType): Boolean {
        val strategy = strategies[strategyType]
        return if (strategy != null) {
            defaultStrategy = strategy
            logger.info("设置默认降级策略: ${strategyType} - ${strategy.name}")
            true
        } else {
            logger.warn("无法设置默认策略，策略不存在: $strategyType")
            false
        }
    }
    
    /**
     * 执行降级处理
     * 
     * @param ctx Netty通道上下文
     * @param exception 触发降级的异常
     * @param context 协议上下文
     * @param preferredStrategy 首选策略类型（可选）
     * @return 处理结果
     */
    fun executeFallback(
        ctx: ChannelHandlerContext,
        exception: CaptureException,
        context: ProtocolContext,
        preferredStrategy: FallbackStrategyType? = null
    ): FallbackResult {
        totalExecutions.incrementAndGet()
        
        try {
            // 选择策略
            val strategy = selectStrategy(exception, context, preferredStrategy)
            
            if (strategy == null) {
                logger.warn("没有可用的降级策略: 异常=${exception.errorCode}")
                failedExecutions.incrementAndGet()
                return FallbackResult.failure(
                    "没有可用的降级策略",
                    mapOf("exception_code" to exception.errorCode)
                )
            }
            
            logger.debug("执行降级策略: ${strategy.name}, 会话=${context.session.id}")
            
            // 执行策略
            val result = strategy.execute(ctx, exception, context)
            
            if (result.success) {
                successfulExecutions.incrementAndGet()
                logger.info("降级策略执行成功: ${strategy.name}, 动作=${result.action}")
            } else {
                failedExecutions.incrementAndGet()
                logger.warn("降级策略执行失败: ${strategy.name}, 原因=${result.message}")
            }
            
            return result
            
        } catch (e: Exception) {
            logger.error("执行降级策略时发生异常", e)
            failedExecutions.incrementAndGet()
            
            throw FallbackException.strategyFailed(
                strategyName = "未知",
                reason = e.message ?: "未知错误",
                context = context,
                cause = e
            )
        }
    }
    
    /**
     * 选择合适的降级策略
     */
    private fun selectStrategy(
        exception: CaptureException,
        context: ProtocolContext,
        preferredStrategy: FallbackStrategyType?
    ): FallbackStrategy? {
        // 1. 如果指定了首选策略，优先使用
        if (preferredStrategy != null) {
            val strategy = strategies[preferredStrategy]
            if (strategy != null && strategy.canHandle(exception, context)) {
                logger.debug("使用首选策略: ${strategy.name}")
                return strategy
            }
        }
        
        // 2. 查找能处理该异常的策略
        for (strategy in strategies.values) {
            if (strategy.canHandle(exception, context)) {
                logger.debug("找到匹配策略: ${strategy.name}")
                return strategy
            }
        }
        
        // 3. 使用默认策略
        if (defaultStrategy != null) {
            logger.debug("使用默认策略: ${defaultStrategy!!.name}")
            return defaultStrategy
        }
        
        // 4. 没有可用策略
        return null
    }
    
    /**
     * 获取策略
     * 
     * @param strategyType 策略类型
     * @return 策略实例，如果不存在则返回null
     */
    fun getStrategy(strategyType: FallbackStrategyType): FallbackStrategy? {
        return strategies[strategyType]
    }
    
    /**
     * 获取所有策略
     * 
     * @return 策略映射
     */
    fun getAllStrategies(): Map<FallbackStrategyType, FallbackStrategy> {
        return strategies.toMap()
    }
    
    /**
     * 获取管理器统计信息
     * 
     * @return 统计信息映射
     */
    fun getStatistics(): Map<String, Any> {
        val total = totalExecutions.get()
        val successful = successfulExecutions.get()
        val failed = failedExecutions.get()
        
        val strategyStats = strategies.mapValues { (_, strategy) ->
            strategy.getStatistics()
        }
        
        return mapOf(
            "total_executions" to total,
            "successful_executions" to successful,
            "failed_executions" to failed,
            "success_rate" to if (total > 0) successful.toDouble() / total else 0.0,
            "registered_strategies" to strategies.keys.map { it.name },
            "default_strategy" to (defaultStrategy?.name ?: "无"),
            "strategy_statistics" to strategyStats
        )
    }
    
    /**
     * 重置统计信息
     */
    fun resetStatistics() {
        totalExecutions.set(0)
        successfulExecutions.set(0)
        failedExecutions.set(0)
        
        // 重置所有策略的统计信息
        strategies.values.forEach { it.resetStatistics() }
        
        logger.info("降级管理器统计信息已重置")
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        strategies.clear()
        defaultStrategy = null
        logger.info("降级管理器已清理")
    }
}
