package com.dev.tools.capture.fallback

import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.model.FallbackStrategyType
import com.dev.tools.capture.model.ProtocolContext
import io.netty.channel.ChannelHandlerContext

/**
 * 降级策略接口
 * 
 * 定义当主要处理流程失败时的降级处理策略
 */
interface FallbackStrategy {
    
    /**
     * 策略类型
     */
    val type: FallbackStrategyType
    
    /**
     * 策略名称
     */
    val name: String
    
    /**
     * 策略描述
     */
    val description: String
    
    /**
     * 检查是否可以处理指定的异常
     * 
     * @param exception 异常
     * @param context 协议上下文
     * @return 是否可以处理
     */
    fun canHandle(exception: CaptureException, context: ProtocolContext): Boolean
    
    /**
     * 执行降级处理
     * 
     * @param ctx Netty通道上下文
     * @param exception 触发降级的异常
     * @param context 协议上下文
     * @return 处理结果
     */
    fun execute(
        ctx: ChannelHandlerContext,
        exception: CaptureException,
        context: ProtocolContext
    ): FallbackResult
    
    /**
     * 获取策略统计信息
     * 
     * @return 统计信息映射
     */
    fun getStatistics(): Map<String, Any>
    
    /**
     * 重置统计信息
     */
    fun resetStatistics()
}

/**
 * 降级处理结果
 */
data class FallbackResult(
    /**
     * 处理是否成功
     */
    val success: Boolean,
    
    /**
     * 结果消息
     */
    val message: String,
    
    /**
     * 处理动作
     */
    val action: FallbackAction,
    
    /**
     * 额外的元数据
     */
    val metadata: Map<String, Any> = emptyMap()
) {
    companion object {
        /**
         * 创建成功结果
         */
        fun success(action: FallbackAction, message: String = "", metadata: Map<String, Any> = emptyMap()): FallbackResult {
            return FallbackResult(true, message, action, metadata)
        }
        
        /**
         * 创建失败结果
         */
        fun failure(message: String, metadata: Map<String, Any> = emptyMap()): FallbackResult {
            return FallbackResult(false, message, FallbackAction.CLOSE_CONNECTION, metadata)
        }
    }
}

/**
 * 降级处理动作
 */
enum class FallbackAction {
    /**
     * 继续处理 - 尝试继续正常流程
     */
    CONTINUE,
    
    /**
     * 切换到隧道模式 - 直接转发数据
     */
    SWITCH_TO_TUNNEL,
    
    /**
     * 关闭连接
     */
    CLOSE_CONNECTION,
    
    /**
     * 重试处理
     */
    RETRY,
    
    /**
     * 记录并忽略
     */
    LOG_AND_IGNORE
}
