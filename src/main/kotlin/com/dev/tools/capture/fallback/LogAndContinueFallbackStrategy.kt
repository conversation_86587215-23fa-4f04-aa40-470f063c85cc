package com.dev.tools.capture.fallback

import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.model.FallbackStrategyType
import com.dev.tools.capture.model.ProtocolContext
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicLong

/**
 * 记录并继续降级策略
 * 
 * 记录错误但继续处理，适用于非关键错误
 */
class LogAndContinueFallbackStrategy : FallbackStrategy {
    
    private val logger = LoggerFactory.getLogger(LogAndContinueFallbackStrategy::class.java)
    
    override val type: FallbackStrategyType = FallbackStrategyType.LOG_AND_CONTINUE
    override val name: String = "记录并继续降级策略"
    override val description: String = "记录错误但继续处理，适用于非关键错误"
    
    // 统计信息
    private val totalExecutions = AtomicLong(0)
    private val errorsLogged = AtomicLong(0)
    private val errorsByType = mutableMapOf<String, AtomicLong>()
    
    override fun canHandle(exception: CaptureException, context: ProtocolContext): Bo<PERSON>an {
        // 只处理非关键错误
        return when (exception.errorCode) {
            "PROTOCOL_001", // 协议解析失败
            "CALLBACK_001", // 回调执行失败
            "MOCK_001",     // Mock处理失败
            "STORAGE_001",  // 存储写入失败
            "STORAGE_002"   // 存储读取失败
            -> true
            else -> false
        }
    }
    
    override fun execute(
        ctx: ChannelHandlerContext,
        exception: CaptureException,
        context: ProtocolContext
    ): FallbackResult {
        totalExecutions.incrementAndGet()
        errorsLogged.incrementAndGet()
        
        // 统计错误类型
        val errorType = exception::class.simpleName ?: "Unknown"
        errorsByType.computeIfAbsent(errorType) { AtomicLong(0) }.incrementAndGet()
        
        logger.warn("记录并继续策略: 会话=${context.session.id}, " +
                   "异常=${exception.errorCode}, 消息=${exception.message}")
        
        try {
            // 记录详细的错误信息
            logErrorDetails(exception, context)
            
            // 尝试清理可能的问题状态
            cleanupErrorState(ctx, exception, context)
            
            logger.debug("错误已记录，继续处理: 会话=${context.session.id}")
            
            return FallbackResult.success(
                FallbackAction.LOG_AND_IGNORE,
                "错误已记录，继续处理",
                mapOf(
                    "error_logged" to true,
                    "error_type" to errorType,
                    "error_code" to exception.errorCode,
                    "session_id" to context.session.id
                )
            )
            
        } catch (e: Exception) {
            logger.error("执行记录并继续策略失败", e)
            
            return FallbackResult.failure(
                "记录并继续策略执行失败: ${e.message}",
                mapOf("error" to e.message!!)
            )
        }
    }
    
    /**
     * 记录错误详细信息
     */
    private fun logErrorDetails(exception: CaptureException, context: ProtocolContext) {
        val errorDetails = buildString {
            appendLine("=== 错误详细信息 ===")
            appendLine("错误代码: ${exception.errorCode}")
            appendLine("错误消息: ${exception.message}")
            appendLine("错误类型: ${exception::class.simpleName}")
            appendLine("发生时间: ${exception.timestamp}")
            appendLine("协议: ${context.protocol}")
            appendLine("会话ID: ${context.session.id}")
            appendLine("来源: ${context.session.source}")
            appendLine("目标: ${context.session.destination}")
            
            if (exception.details.isNotEmpty()) {
                appendLine("详细信息:")
                exception.details.forEach { (key, value) ->
                    appendLine("  $key: $value")
                }
            }
            
            exception.cause?.let {
                appendLine("原因: ${it.message}")
                appendLine("堆栈跟踪: ${it.stackTraceToString()}")
            }
        }
        
        logger.debug(errorDetails)
    }
    
    /**
     * 清理错误状态
     */
    private fun cleanupErrorState(
        ctx: ChannelHandlerContext,
        exception: CaptureException,
        context: ProtocolContext
    ) {
        try {
            when (exception.errorCode) {
                "CALLBACK_001" -> {
                    // 回调执行失败，可能需要移除有问题的回调
                    logger.debug("回调执行失败，考虑移除有问题的回调")
                }
                "MOCK_001" -> {
                    // Mock处理失败，可能需要禁用有问题的规则
                    logger.debug("Mock处理失败，考虑禁用有问题的规则")
                }
                "STORAGE_001", "STORAGE_002" -> {
                    // 存储失败，可能需要切换存储后端
                    logger.debug("存储操作失败，考虑切换存储后端")
                }
                else -> {
                    logger.debug("通用错误清理")
                }
            }
        } catch (e: Exception) {
            logger.warn("清理错误状态时发生异常", e)
        }
    }
    
    override fun getStatistics(): Map<String, Any> {
        val errorTypeStats = errorsByType.mapValues { it.value.get() }
        
        return mapOf(
            "strategy_name" to name,
            "strategy_type" to type.name,
            "total_executions" to totalExecutions.get(),
            "errors_logged" to errorsLogged.get(),
            "errors_by_type" to errorTypeStats
        )
    }
    
    override fun resetStatistics() {
        totalExecutions.set(0)
        errorsLogged.set(0)
        errorsByType.clear()
        logger.info("记录并继续降级策略统计信息已重置")
    }
}
