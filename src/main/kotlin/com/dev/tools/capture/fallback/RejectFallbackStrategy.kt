package com.dev.tools.capture.fallback

import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.model.FallbackStrategyType
import com.dev.tools.capture.model.ProtocolContext
import io.netty.channel.ChannelHandlerContext
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicLong

/**
 * 拒绝连接降级策略
 * 
 * 当处理失败时直接关闭连接
 */
class RejectFallbackStrategy : FallbackStrategy {
    
    private val logger = LoggerFactory.getLogger(RejectFallbackStrategy::class.java)
    
    override val type: FallbackStrategyType = FallbackStrategyType.REJECT
    override val name: String = "拒绝连接降级策略"
    override val description: String = "处理失败时直接关闭连接"
    
    // 统计信息
    private val totalExecutions = AtomicLong(0)
    private val connectionsRejected = AtomicLong(0)
    
    override fun canHandle(exception: CaptureException, context: ProtocolContext): Boolean {
        // 可以处理所有类型的异常
        return true
    }
    
    override fun execute(
        ctx: ChannelHandlerContext,
        exception: CaptureException,
        context: ProtocolContext
    ): FallbackResult {
        totalExecutions.incrementAndGet()
        connectionsRejected.incrementAndGet()
        
        logger.info("执行拒绝连接策略: 会话=${context.session.id}, 异常=${exception.errorCode}")
        
        try {
            // 记录拒绝原因
            val rejectReason = "处理失败: ${exception.message}"
            
            // 关闭连接
            ctx.close()
            
            logger.debug("连接已关闭: 会话=${context.session.id}, 原因=$rejectReason")
            
            return FallbackResult.success(
                FallbackAction.CLOSE_CONNECTION,
                "连接已关闭: $rejectReason",
                mapOf(
                    "reject_reason" to rejectReason,
                    "session_id" to context.session.id,
                    "exception_code" to exception.errorCode
                )
            )
            
        } catch (e: Exception) {
            logger.error("执行拒绝连接策略失败", e)
            
            return FallbackResult.failure(
                "拒绝连接策略执行失败: ${e.message}",
                mapOf("error" to e.message!!)
            )
        }
    }
    
    override fun getStatistics(): Map<String, Any> {
        return mapOf(
            "strategy_name" to name,
            "strategy_type" to type.name,
            "total_executions" to totalExecutions.get(),
            "connections_rejected" to connectionsRejected.get()
        )
    }
    
    override fun resetStatistics() {
        totalExecutions.set(0)
        connectionsRejected.set(0)
        logger.info("拒绝连接降级策略统计信息已重置")
    }
}
