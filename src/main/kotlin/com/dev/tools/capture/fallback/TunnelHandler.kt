package com.dev.tools.capture.fallback

import com.dev.tools.capture.model.ProtocolContext
import io.netty.buffer.ByteBuf
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.ChannelInboundHandlerAdapter
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicLong

/**
 * 隧道处理器
 * 
 * 在隧道模式下直接转发数据，不进行解密或解析
 */
class TunnelHandler(private val context: ProtocolContext) : ChannelInboundHandlerAdapter() {
    
    private val logger = LoggerFactory.getLogger(TunnelHandler::class.java)
    
    // 统计信息
    private val bytesTransferred = AtomicLong(0)
    private val packetsTransferred = AtomicLong(0)
    
    override fun channelActive(ctx: ChannelHandlerContext) {
        logger.debug("隧道连接激活: 会话=${context.session.id}")
        super.channelActive(ctx)
    }
    
    override fun channelRead(ctx: ChannelHandlerContext, msg: Any) {
        if (msg is ByteBuf) {
            val dataSize = msg.readableBytes()
            
            // 记录统计信息
            bytesTransferred.addAndGet(dataSize.toLong())
            packetsTransferred.incrementAndGet()
            
            logger.trace("隧道模式转发数据: ${dataSize}字节, 会话=${context.session.id}")
            
            // 直接转发数据，不进行任何处理
            ctx.fireChannelRead(msg)
        } else {
            // 非ByteBuf消息也直接转发
            ctx.fireChannelRead(msg)
        }
    }
    
    override fun channelInactive(ctx: ChannelHandlerContext) {
        logger.debug("隧道连接断开: 会话=${context.session.id}, " +
                    "传输字节=${bytesTransferred.get()}, " +
                    "传输包数=${packetsTransferred.get()}")
        super.channelInactive(ctx)
    }
    
    override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
        logger.warn("隧道处理器异常: 会话=${context.session.id}", cause)
        
        // 在隧道模式下，大多数异常都应该被忽略
        // 因为我们不对数据进行解析，所以解析错误是正常的
        when (cause) {
            is javax.net.ssl.SSLException -> {
                // SSL异常在隧道模式下是预期的，忽略
                logger.trace("隧道模式下的SSL异常（预期）: ${cause.message}")
            }
            is java.io.IOException -> {
                // IO异常可能表示连接问题，关闭连接
                logger.debug("隧道模式下的IO异常，关闭连接: ${cause.message}")
                ctx.close()
            }
            else -> {
                // 其他异常记录但不关闭连接
                logger.debug("隧道模式下的未知异常: ${cause.message}")
            }
        }
    }
    
    /**
     * 获取传输统计信息
     */
    fun getTransferStatistics(): Map<String, Any> {
        return mapOf(
            "bytes_transferred" to bytesTransferred.get(),
            "packets_transferred" to packetsTransferred.get(),
            "session_id" to context.session.id,
            "protocol" to context.protocol.name
        )
    }
}
