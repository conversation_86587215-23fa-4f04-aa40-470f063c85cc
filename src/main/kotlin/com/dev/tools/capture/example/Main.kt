package com.dev.tools.capture.example

import com.dev.tools.capture.PacketCaptureSDK
import com.dev.tools.capture.api.PacketCallback
import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.model.*
import com.dev.tools.capture.mock.MockRuleBuilder
import com.dev.tools.capture.mock.MockTemplates
import com.dev.tools.capture.security.CertificateManager
import com.dev.tools.capture.security.CertificateDownloadServer
import java.io.File
import java.net.InetAddress
import java.net.NetworkInterface
import java.util.concurrent.CountDownLatch
import kotlin.system.exitProcess

/**
 * 网络请求拦截器示例
 *
 * 启动一个HTTP/HTTPS代理服务器，拦截并分析真实的网络请求
 */
fun main() {
    printBanner()
    println("启动HTTP/HTTPS代理服务器，拦截真实网络流量")
    println()

    try {
        // 1. 创建生产级配置
        val config = SDKConfig(
            port = 8888,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET),
            enableSslMitm = false,  // 启用SSL中间人解密
            sslFallbackStrategy = FallbackStrategyType.TUNNEL, // SSL失败时使用隧道模式

            // SSL证书配置
            sslCertificatePath = "./temp/ssl/certificates",
            sslKeyStorePath = "./temp/ssl/keystore.p12",
            sslKeyStorePassword = "packetcapture123",
            enableCertificateDownload = true,
            certificateDownloadPort = 8889,

            // 存储配置
            storageConfig = StorageConfig.FILE, // 使用文件存储
            dataStoragePath = "./temp/data",
            logStoragePath = "./temp/logs",

            // 网络配置
            maxConnections = 1000,
            connectionTimeout = 30000,
            readTimeout = 60000,
            writeTimeout = 60000,
            bufferSize = 32768,

            // 日志配置
            enableLogging = true,
            logLevel = LogLevel.DEBUG,

            // 其他配置
            enableMetrics = true,
            enableDataMasking = false, // 调试时不脱敏
            customAttributes = mapOf(
                "mode" to "interceptor",
                "version" to "1.0.0",
                "bind_address" to "0.0.0.0" // 监听所有网络接口
            )
        )

        println("代理服务器配置:")
        println("  监听端口: ${config.port}")
        println("  支持协议: ${config.protocols.joinToString { it.name }}")
        println("  SSL解密: ${config.enableSslMitm}")
        println("  最大连接: ${config.maxConnections}")
        println()

        // 2. 初始化SSL证书管理
        println("初始化SSL证书管理...")
        val certificateManager = CertificateManager(
            config.sslCertificatePath,
            config.sslKeyStorePath,
            config.sslKeyStorePassword
        )

        if (!certificateManager.initialize()) {
            println("❌ SSL证书初始化失败")
            exitProcess(1)
        }

        // 3. 启动证书下载服务
        var certificateServer: CertificateDownloadServer? = null
        if (config.enableCertificateDownload) {
            println("启动证书下载服务...")
            certificateServer = CertificateDownloadServer(config.certificateDownloadPort, certificateManager)
            if (!certificateServer.start()) {
                println("❌ 证书下载服务启动失败")
                exitProcess(1)
            }
        }

        // 4. 创建必要的目录
        createDirectories(config)

        // 5. 创建SDK实例
        val sdk = PacketCaptureSDK(config)

        // 6. 注册网络流量分析回调
        val trafficAnalyzer = NetworkTrafficAnalyzer()
        val callbackId = sdk.registerCallback(trafficAnalyzer)
        println("注册流量分析器: $callbackId")

        // 7. 添加一些有用的Mock规则
        setupMockRules(sdk)

        // 8. 设置优雅关闭
        val shutdownLatch = CountDownLatch(1)
        Runtime.getRuntime().addShutdownHook(Thread {
            println("\n正在关闭服务...")
            sdk.stop()
            certificateServer?.stop()
            shutdownLatch.countDown()
        })

        // 6. 启动代理服务器
        println("启动代理服务器...")
        sdk.start()

        println("✅ 代理服务器已启动!")
        println()
        printUsageInstructions(config.port)
        println("🔍 实时流量监控中...")
        println("=" * 80)

        // 7. 等待关闭信号
        shutdownLatch.await()
        println("代理服务器已停止")

    } catch (e: Exception) {
        println("❌ 启动失败: ${e.message}")
        e.printStackTrace()
        exitProcess(1)
    }
}

/**
 * 网络流量分析器
 *
 * 实时分析拦截到的网络流量
 */
class NetworkTrafficAnalyzer : PacketCallback {
    private var requestCount = 0
    private var responseCount = 0
    private val hostStats = mutableMapOf<String, Int>()
    private val methodStats = mutableMapOf<String, Int>()
    private val statusStats = mutableMapOf<String, Int>()

    override fun onPacketReceived(packet: PacketData, context: ProtocolContext) {
        when (packet.direction) {
            PacketData.PacketDirection.CLIENT_TO_SERVER -> analyzeRequest(packet, context)
            PacketData.PacketDirection.SERVER_TO_CLIENT -> analyzeResponse(packet, context)
            else -> {}
        }
    }

    private fun analyzeRequest(packet: PacketData, context: ProtocolContext) {
        requestCount++

        // 检查是否是通过元数据传递的请求信息
        if (packet.metadata["type"] == "request") {
            val method = packet.metadata["method"] ?: "UNKNOWN"
            val uri = packet.metadata["uri"] ?: "/"
            val host = packet.metadata["host"] ?: "unknown"
            val contentLength = packet.metadata["content_length"] ?: "0"

            methodStats[method] = methodStats.getOrDefault(method, 0) + 1
            hostStats[host] = hostStats.getOrDefault(host, 0) + 1

            println("📤 [${context.protocol}] $method https://$host$uri")
            println("   📦 Content-Length: $contentLength bytes")

            // 显示重要的请求头
            val userAgent = packet.metadata["header_user-agent"]
            if (userAgent != null && userAgent.length > 50) {
                println("   📱 User-Agent: ${userAgent.take(50)}...")
            }

            val contentType = packet.metadata["header_content-type"]
            if (contentType != null) {
                println("   📄 Content-Type: $contentType")
            }

            // 如果有请求体内容，显示部分内容
            if (packet.size > 0 && contentType?.contains("json") == true) {
                val content = packet.rawDataAsString
                if (content.isNotEmpty()) {
                    println("   📝 Request Body: ${content.take(100)}${if (content.length > 100) "..." else ""}")
                }
            }
        } else {
            // 兼容旧格式的解析
            val content = packet.rawDataAsString
            if (content.startsWith("GET") || content.startsWith("POST") ||
                content.startsWith("PUT") || content.startsWith("DELETE")) {

                val lines = content.split("\r\n", "\n")
                if (lines.isNotEmpty()) {
                    val requestLine = lines[0]
                    val parts = requestLine.split(" ")
                    if (parts.size >= 3) {
                        val method = parts[0]
                        val path = parts[1]

                        methodStats[method] = methodStats.getOrDefault(method, 0) + 1

                        // 提取Host
                        val hostLine = lines.find { it.startsWith("Host:", ignoreCase = true) }
                        val host = hostLine?.substringAfter(":")?.trim() ?: "unknown"
                        hostStats[host] = hostStats.getOrDefault(host, 0) + 1

                        println("📤 [${context.protocol}] $method $host$path")

                        // 显示一些有趣的头部
                        val userAgent = lines.find { it.startsWith("User-Agent:", ignoreCase = true) }
                            ?.substringAfter(":")?.trim()
                        if (userAgent != null && userAgent.length > 50) {
                            println("   📱 User-Agent: ${userAgent.take(50)}...")
                        }
                    }
                }
            }
        }
    }

    private fun analyzeResponse(packet: PacketData, context: ProtocolContext) {
        responseCount++

        // 检查是否是通过元数据传递的响应信息
        if (packet.metadata["type"] == "response") {
            val statusCode = packet.metadata["status_code"] ?: "000"
            val statusMessage = packet.metadata["status_message"] ?: "Unknown"
            val contentLength = packet.metadata["content_length"] ?: "0"

            statusStats[statusCode] = statusStats.getOrDefault(statusCode, 0) + 1

            val statusEmoji = when (statusCode.first()) {
                '2' -> "✅"
                '3' -> "🔄"
                '4' -> "⚠️"
                '5' -> "❌"
                else -> "📥"
            }

            println("$statusEmoji [$statusCode] $statusMessage ($contentLength bytes)")

            // 显示重要的响应头
            val contentType = packet.metadata["header_content-type"]
            if (contentType != null) {
                println("   📄 Content-Type: $contentType")
            }

            val server = packet.metadata["header_server"]
            if (server != null) {
                println("   🖥️ Server: $server")
            }

            // 如果有响应体内容，显示部分内容
            if (packet.size > 0 && contentType?.contains("json") == true) {
                val content = packet.rawDataAsString
                if (content.isNotEmpty()) {
                    println("   📝 Response Body: ${content.take(100)}${if (content.length > 100) "..." else ""}")
                }
            }
        } else {
            // 兼容旧格式的解析
            val content = packet.rawDataAsString
            if (content.startsWith("HTTP/")) {
                val lines = content.split("\r\n", "\n")
                if (lines.isNotEmpty()) {
                    val statusLine = lines[0]
                    val parts = statusLine.split(" ")
                    if (parts.size >= 3) {
                        val statusCode = parts[1]
                        val statusText = parts.drop(2).joinToString(" ")

                        statusStats[statusCode] = statusStats.getOrDefault(statusCode, 0) + 1

                        val statusEmoji = when (statusCode.first()) {
                            '2' -> "✅"
                            '3' -> "🔄"
                            '4' -> "⚠️"
                            '5' -> "❌"
                            else -> "📥"
                        }

                        println("$statusEmoji [$statusCode] $statusText (${packet.size} bytes)")

                        // 显示内容类型
                        val contentType = lines.find { it.startsWith("Content-Type:", ignoreCase = true) }
                            ?.substringAfter(":")?.trim()
                        if (contentType != null) {
                            println("   📄 Content-Type: $contentType")
                        }
                    }
                }
            }
        }

        // 每100个响应显示一次统计
        if (responseCount % 100 == 0) {
            printStatistics()
        }
    }

    private fun printStatistics() {
        println("\n📊 流量统计 (请求: $requestCount, 响应: $responseCount)")

        if (hostStats.isNotEmpty()) {
            println("🌐 热门域名:")
            hostStats.toList().sortedByDescending { it.second }.take(5).forEach { (host, count) ->
                println("   $host: $count 次")
            }
        }

        if (methodStats.isNotEmpty()) {
            println("🔧 HTTP方法:")
            methodStats.forEach { (method, count) ->
                println("   $method: $count 次")
            }
        }

        if (statusStats.isNotEmpty()) {
            println("📈 状态码:")
            statusStats.toList().sortedByDescending { it.second }.take(5).forEach { (status, count) ->
                println("   $status: $count 次")
            }
        }

        println("=" * 80)
    }

    override fun onError(exception: CaptureException, context: ProtocolContext?) {
        println("❌ 流量分析错误: ${exception.message}")
    }
}

/**
 * 设置Mock规则
 *
 * 添加一些有用的Mock规则来演示功能
 */
private fun setupMockRules(sdk: PacketCaptureSDK) {
    println("设置Mock规则...")

    // 1. Mock一些常见的API端点
    val apiMockRule = MockRuleBuilder.create("通用API Mock")
        .description("拦截常见API请求并返回Mock数据")
        .priority(10)
        .matchHttpMethod("GET")
        .matchUrlPattern(".*/api/(users|user|profile).*")
        .respondWithJson("""
            {
                "success": true,
                "data": {
                    "id": 12345,
                    "name": "Mock User",
                    "email": "<EMAIL>",
                    "avatar": "https://via.placeholder.com/150",
                    "created_at": "${java.time.Instant.now()}"
                },
                "message": "这是一个Mock响应",
                "mock": true
            }
        """.trimIndent())
        .build()

    sdk.addMockRule(apiMockRule)
    println("✅ 添加API Mock规则")

    // 2. Mock广告请求（返回空响应）
    val adBlockRule = MockRuleBuilder.create("广告拦截")
        .description("拦截广告请求")
        .priority(20)
        .matchUrlPattern(".*(ads|advertisement|doubleclick|googlesyndication).*")
        .respondWithJson("""{"blocked": true, "reason": "广告已拦截"}""", 204)
        .build()

    sdk.addMockRule(adBlockRule)
    println("✅ 添加广告拦截规则")

    // 3. Mock错误页面
    val errorMockRule = MockRuleBuilder.create("错误页面Mock")
        .description("模拟服务器错误")
        .matchUrlPattern(".*/error.*")
        .respondWithError(500, """
            {
                "error": "服务器内部错误",
                "code": "INTERNAL_ERROR",
                "message": "这是一个模拟的服务器错误",
                "timestamp": "${java.time.Instant.now()}"
            }
        """.trimIndent(), "application/json")
        .build()

    sdk.addMockRule(errorMockRule)
    println("✅ 添加错误Mock规则")

    // 4. Mock登录接口
    val loginMock = MockTemplates.Auth.loginSuccess("intercepted_user", "mock_token_${System.currentTimeMillis()}")
    sdk.addMockRule(loginMock)
    println("✅ 添加登录Mock规则")

    // 5. Mock静态资源
    val staticMock = MockTemplates.Files.htmlPage(
        "拦截页面",
        """
        <h1>🎯 请求已被拦截</h1>
        <p>这个页面是由PacketCapture SDK生成的Mock响应。</p>
        <p>原始请求已被成功拦截和分析。</p>
        <p>时间: ${java.time.LocalDateTime.now()}</p>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            h1 { color: #2196F3; }
            p { line-height: 1.6; }
        </style>
        """.trimIndent()
    )
    sdk.addMockRule(staticMock)
    println("✅ 添加静态页面Mock规则")

    println("Mock规则设置完成，共添加了 ${sdk.getMockRules().size} 个规则")
    println()
}

/**
 * 获取本机IP地址
 */
private fun getLocalIPAddresses(): List<String> {
    val addresses = mutableListOf<String>()

    try {
        val interfaces = NetworkInterface.getNetworkInterfaces()
        while (interfaces.hasMoreElements()) {
            val networkInterface = interfaces.nextElement()

            // 跳过回环接口和未启用的接口
            if (networkInterface.isLoopback || !networkInterface.isUp) {
                continue
            }

            val inetAddresses = networkInterface.inetAddresses
            while (inetAddresses.hasMoreElements()) {
                val inetAddress = inetAddresses.nextElement()

                // 只获取IPv4地址，排除链路本地地址
                if (!inetAddress.isLoopbackAddress &&
                    !inetAddress.isLinkLocalAddress &&
                    inetAddress is java.net.Inet4Address) {
                    addresses.add(inetAddress.hostAddress)
                }
            }
        }
    } catch (e: Exception) {
        println("⚠️ 获取IP地址失败: ${e.message}")
    }

    return addresses
}

/**
 * 打印使用说明
 */
private fun printUsageInstructions(port: Int) {
    val localIPs = getLocalIPAddresses()
    val primaryIP = localIPs.firstOrNull() ?: "127.0.0.1"

    println("🌐 代理服务器地址:")
    println("   本机访问: 127.0.0.1:$port")
    if (localIPs.isNotEmpty()) {
        println("   局域网访问: ${localIPs.joinToString(", ") { "$it:$port" }}")
        println()
        println("💡 推荐使用: $primaryIP:$port (适用于手机等其他设备)")
    }
    println()
    println("🔧 代理设置说明:")
    println("   Windows (本机):")
    println("     1. 打开 设置 > 网络和Internet > 代理")
    println("     2. 启用 '使用代理服务器'")
    println("     3. 地址: 127.0.0.1  端口: $port")
    println()
    println("   macOS (本机):")
    println("     1. 打开 系统偏好设置 > 网络")
    println("     2. 选择网络连接 > 高级 > 代理")
    println("     3. 勾选 'Web代理(HTTP)' 和 '安全Web代理(HTTPS)'")
    println("     4. 服务器: 127.0.0.1  端口: $port")
    println()

    if (localIPs.isNotEmpty()) {
        val recommendedIP = localIPs.first()
        println("   Android/iOS (手机设备):")
        println("     📱 确保手机和电脑在同一WiFi网络")
        println("     1. 长按WiFi网络 > 修改网络 (Android) 或")
        println("        设置 > WiFi > 点击网络 'i' (iOS)")
        println("     2. 代理设置 > 手动")
        println("     3. 主机名/服务器: $recommendedIP")
        println("     4. 端口: $port")
        println()

        println("   其他设备:")
        localIPs.forEach { ip ->
            println("     - $ip:$port")
        }
        println()
    }
    println("⚠️  HTTPS流量需要安装SSL证书才能解密")
    println("   证书将在首次HTTPS请求时自动生成")
    println()
}

/**
 * 打印横幅
 */
private fun printBanner() {
    println("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    PacketCapture SDK                         ║
    ║                   网络请求拦截器                              ║
    ║                                                              ║
    ║  🌐 支持 HTTP/HTTPS/WebSocket 协议                           ║
    ║  🔒 SSL/TLS 中间人解密                                       ║
    ║  🎯 智能Mock规则引擎                                         ║
    ║  📊 实时流量分析                                             ║
    ║  🛡️  降级处理策略                                            ║
    ╚══════════════════════════════════════════════════════════════╝
    """.trimIndent())
    println()
}

/**
 * 创建必要的目录
 */
private fun createDirectories(config: SDKConfig) {
    val directories = listOf(
        config.sslCertificatePath,
        config.dataStoragePath,
        config.logStoragePath,
        File(config.sslKeyStorePath).parent
    )

    directories.forEach { path ->
        if (path != null) {
            val dir = File(path)
            if (!dir.exists()) {
                if (dir.mkdirs()) {
                    println("✅ 创建目录: ${dir.absolutePath}")
                } else {
                    println("⚠️ 创建目录失败: ${dir.absolutePath}")
                }
            }
        }
    }
}

/**
 * 扩展函数：重复字符串
 */
private operator fun String.times(n: Int): String = this.repeat(n)
