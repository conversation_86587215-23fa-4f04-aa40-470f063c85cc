package com.dev.tools.capture.core

import com.dev.tools.capture.callback.CallbackManager
import com.dev.tools.capture.mock.MockEngine
import com.dev.tools.capture.model.*
import com.dev.tools.capture.security.SslManager
import io.netty.bootstrap.Bootstrap
import io.netty.bootstrap.ServerBootstrap
import io.netty.buffer.ByteBuf
import io.netty.buffer.Unpooled
import io.netty.channel.*
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.SocketChannel
import io.netty.channel.socket.nio.NioSocketChannel
import io.netty.channel.socket.nio.NioServerSocketChannel
import io.netty.handler.codec.http.*
import io.netty.handler.logging.LogLevel
import io.netty.handler.logging.LoggingHandler
import io.netty.handler.ssl.SslContext
import io.netty.handler.ssl.SslContextBuilder
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import io.netty.handler.ssl.JdkSslContext
import io.netty.handler.ssl.ClientAuth
import io.netty.handler.codec.DecoderException
import io.netty.util.AttributeKey
import javax.net.ssl.SSLProtocolException
import io.netty.util.concurrent.Future
import java.io.File
import java.io.FileInputStream
import java.net.URI
import java.security.KeyStore
import javax.net.ssl.KeyManagerFactory
import org.slf4j.LoggerFactory
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 抓包引擎核心类
 * 
 * 负责网络流量的捕获、处理和分发
 * 
 * @property config SDK配置
 * @property callbackManager 回调管理器
 * @property mockEngine Mock引擎
 */
class PacketCaptureEngine(
    private val config: SDKConfig,
    private val callbackManager: CallbackManager,
    private val mockEngine: MockEngine
) {

    private val logger = LoggerFactory.getLogger(PacketCaptureEngine::class.java)

    // 运行状态
    private val isRunning = AtomicBoolean(false)

    // 统计信息
    private val totalPacketsProcessed = AtomicLong(0)
    private val activeConnections = AtomicLong(0)

    // Netty组件
    private var bossGroup: EventLoopGroup? = null
    private var workerGroup: EventLoopGroup? = null
    private var serverChannel: Channel? = null

    // SSL管理器
    private val sslManager = SslManager()

    // SSL上下文
    private var serverSslContext: SslContext? = null
    private var clientSslContext: SslContext? = null

    // 白名单域名列表 (使用隧道模式，不解密)
    private val whitelistDomains = setOf(
        "connectivitycheck.platform.hicloud.com",
        "connectivitycheck.cbg-app.huawei.com",
        "apm-and.zhipin.com",
        "magpie.xhscdn.com",
        "api.zhipin.com"
    )
    
    /**
     * 启动抓包引擎
     */
    fun start() {
        if (isRunning.get()) {
            logger.warn("抓包引擎已经在运行中")
            return
        }

        logger.info("启动抓包引擎，端口: ${config.port}")

        try {
            // 初始化SSL上下文
            initializeSslContexts()

            // 创建Netty事件循环组
            bossGroup = NioEventLoopGroup(1)
            workerGroup = NioEventLoopGroup()

            // 创建服务器启动器
            val bootstrap = ServerBootstrap()
            bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel::class.java)
                .option(ChannelOption.SO_BACKLOG, 128)
                .childOption(ChannelOption.SO_KEEPALIVE, true)
                .childOption(ChannelOption.TCP_NODELAY, true)
                .handler(LoggingHandler(LogLevel.DEBUG))
                .childHandler(object : ChannelInitializer<SocketChannel>() {
                    override fun initChannel(ch: SocketChannel) {
                        val pipeline = ch.pipeline()

                        // HTTP编解码器
                        pipeline.addLast("httpCodec", HttpServerCodec())
                        pipeline.addLast("httpAggregator", HttpObjectAggregator(65536))

                        // 自定义处理器
                        pipeline.addLast("proxyHandler", ProxyHandler())
                    }
                })

            // 绑定端口并启动服务器
            val future = bootstrap.bind(config.port).sync()
            serverChannel = future.channel()

            isRunning.set(true)
            logger.info("抓包引擎启动成功，监听端口: ${config.port}")

        } catch (e: Exception) {
            logger.error("启动抓包引擎失败", e)
            cleanup()
            throw e
        }
    }
    
    /**
     * 停止抓包引擎
     */
    fun stop() {
        if (!isRunning.get()) {
            logger.warn("抓包引擎未在运行中")
            return
        }

        logger.info("停止抓包引擎")

        try {
            cleanup()
            isRunning.set(false)
            logger.info("抓包引擎已停止")

        } catch (e: Exception) {
            logger.error("停止抓包引擎时发生错误", e)
        }
    }

    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            // 关闭服务器通道
            serverChannel?.close()?.sync()

            // 关闭事件循环组
            workerGroup?.shutdownGracefully()
            bossGroup?.shutdownGracefully()

            // 清空引用
            serverChannel = null
            workerGroup = null
            bossGroup = null

        } catch (e: Exception) {
            logger.warn("清理资源时发生错误", e)
        }
    }
    
    /**
     * 获取活跃连接数
     */
    fun getActiveConnectionCount(): Long = activeConnections.get()
    
    /**
     * 获取已处理的数据包总数
     */
    fun getTotalPacketsProcessed(): Long = totalPacketsProcessed.get()
    
    /**
     * 检查是否正在运行
     */
    fun isRunning(): Boolean = isRunning.get()

    /**
     * 初始化SSL上下文
     */
    private fun initializeSslContexts() {
        if (!config.enableSslMitm) {
            logger.info("SSL中间人解密已禁用，跳过SSL上下文初始化")
            return
        }

        try {
            // 初始化SSL管理器
            sslManager.initialize()

            // 创建客户端SSL上下文（用于连接目标服务器）
            clientSslContext = SslContextBuilder.forClient()
                .trustManager(InsecureTrustManagerFactory.INSTANCE)
                .build()

            logger.info("SSL上下文初始化成功，SSL中间人解密已启用")

        } catch (e: Exception) {
            logger.error("SSL上下文初始化失败", e)
            throw e
        }
    }

    /**
     * 代理处理器
     */
    private inner class ProxyHandler : SimpleChannelInboundHandler<FullHttpRequest>() {

        override fun channelActive(ctx: ChannelHandlerContext) {
            activeConnections.incrementAndGet()
            logger.debug("新连接建立: ${ctx.channel().remoteAddress()}")
            super.channelActive(ctx)
        }

        override fun channelInactive(ctx: ChannelHandlerContext) {
            activeConnections.decrementAndGet()
            logger.debug("连接关闭: ${ctx.channel().remoteAddress()}")
            super.channelInactive(ctx)
        }

        override fun channelRead0(ctx: ChannelHandlerContext, request: FullHttpRequest) {
            totalPacketsProcessed.incrementAndGet()

            val method = request.method()
            val uri = request.uri()

            logger.debug("收到请求: ${method.name()} $uri")

            when (method) {
                HttpMethod.CONNECT -> {
                    // HTTPS隧道请求
                    handleConnectRequest(ctx, request)
                }
                else -> {
                    // HTTP请求
                    handleHttpRequest(ctx, request)
                }
            }
        }

        /**
         * 处理CONNECT请求（HTTPS隧道）
         */
        private fun handleConnectRequest(ctx: ChannelHandlerContext, request: FullHttpRequest) {
            val uri = request.uri()
            val parts = uri.split(":")

            if (parts.size != 2) {
                sendErrorResponse(ctx, HttpResponseStatus.BAD_REQUEST, "Invalid CONNECT URI: $uri")
                return
            }

            val host = parts[0]
            val port = parts[1].toIntOrNull() ?: 443

            // 保存目标地址信息到channel属性中，用于异常处理时的降级
            ctx.channel().attr(AttributeKey.valueOf<String>("target_host")).set(host)
            ctx.channel().attr(AttributeKey.valueOf<Int>("target_port")).set(port)

            // 通知CONNECT请求
            notifyConnectRequest(ctx, request, host, port)

            // 检查是否是白名单域名（使用隧道模式）
            val isWhitelistDomain = whitelistDomains.any { host.contains(it) }

            if (isWhitelistDomain) {
                // 静默处理华为连接检查
                if (host.contains("connectivitycheck")) {
                    logger.debug("🚫 忽略连接检查域名: $host:$port")
                    handleTunnelConnect(ctx, host, port, silent = true)
                } else {
                    logger.info("⚪ 白名单域名HTTPS CONNECT请求: $host:$port")
                    logger.info("🔗 使用隧道模式 (不解密): $host:$port")
                    handleTunnelConnect(ctx, host, port)
                }
                return
            }

            // 其他所有域名都尝试SSL中间人解密
            logger.info("🎯 HTTPS CONNECT请求: $host:$port")

            if (clientSslContext != null) {
                logger.info("🔓 启用SSL中间人解密: $host:$port")
                handleSslMitmConnect(ctx, host, port)
            } else {
                logger.warn("⚠️ SSL上下文未初始化，使用隧道模式: $host:$port")
                handleTunnelConnect(ctx, host, port)
            }
        }

        /**
         * 处理SSL中间人解密连接
         */
        private fun handleSslMitmConnect(ctx: ChannelHandlerContext, host: String, port: Int) {
            logger.info("🔓 启用SSL中间人解密: $host:$port")

            // 发送200 Connection Established响应
            val response = DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK)
            response.headers().set("Connection", "keep-alive")

            ctx.writeAndFlush(response).addListener { writeFuture ->
                if (writeFuture.isSuccess) {
                    // 标记CONNECT响应已发送
                    ctx.channel().attr(AttributeKey.valueOf<Boolean>("connect_response_sent")).set(true)

                    try {
                        // 移除HTTP编解码器
                        ctx.pipeline().remove("httpCodec")
                        ctx.pipeline().remove("httpAggregator")

                        // 为特定主机创建SSL上下文
                        val hostSslContext = sslManager.createSslContext(host)

                        // 获取证书和私钥
                        val certificate = sslManager.getCertificate(host)
                        if (certificate == null) {
                            logger.error("无法获取主机证书: $host")
                            ctx.close()
                            return@addListener
                        }

                        // 创建Netty SSL上下文，使用JDK SSL上下文
                        val nettySslContext = JdkSslContext(hostSslContext, true, ClientAuth.NONE)

                        // 添加SSL处理器（服务器模式，用动态生成的证书）
                        val sslHandler = nettySslContext.newHandler(ctx.alloc(), host, port)
                        ctx.pipeline().addLast("ssl", sslHandler)

                        // 添加SSL握手完成监听器
                        sslHandler.handshakeFuture().addListener { handshakeResult: Future<in Channel> ->
                            if (handshakeResult.isSuccess) {
                                logger.info("✅ SSL握手成功: $host:$port")

                                try {
                                    // 添加HTTP编解码器来处理解密后的HTTP请求
                                    ctx.pipeline().addLast("httpCodec", HttpServerCodec())
                                    ctx.pipeline().addLast("httpAggregator", HttpObjectAggregator(65536))

                                    // 添加HTTPS内容处理器
                                    ctx.pipeline().addLast("httpsHandler", HttpsContentHandler(host, port))

                                    logger.info("🎯 SSL中间人解密设置完成: $host:$port")
                                } catch (e: Exception) {
                                    logger.error("❌ 添加HTTP处理器失败: $host:$port", e)
                                    ctx.close()
                                }
                            } else {
                                val cause = handshakeResult.cause()
                                logger.warn("⚠️ SSL握手失败: $host:$port - ${cause?.message}")

                                when {
                                    cause is java.nio.channels.ClosedChannelException -> {
                                        logger.info("💡 连接被客户端关闭，可能是证书验证失败")
                                    }
                                    cause?.message?.contains("certificate_unknown") == true -> {
                                        logger.info("💡 SSL证书被拒绝，客户端未安装根证书")
                                    }
                                    cause?.message?.contains("Unexpected handshake message") == true -> {
                                        logger.info("💡 SSL握手协议错误，可能是证书不匹配")
                                    }
                                    else -> {
                                        logger.info("💡 其他SSL握手错误: ${cause?.javaClass?.simpleName}")
                                    }
                                }

                                // 立即降级到隧道模式
                                logger.warn("🔄 SSL解密失败，立即降级到隧道模式: $host:$port")
                                fallbackToTunnel(ctx, host, port)
                            }
                        }

                    } catch (e: Exception) {
                        logger.error("❌ SSL中间人解密设置失败: $host:$port", e)
                        ctx.close()
                    }
                } else {
                    logger.error("❌ 发送CONNECT响应失败: $host:$port")
                    ctx.close()
                }
            }
        }

        /**
         * 通知CONNECT请求
         */
        private fun notifyConnectRequest(ctx: ChannelHandlerContext, request: FullHttpRequest, host: String, port: Int) {
            try {
                // 创建CONNECT请求的数据包
                val connectContent = "CONNECT $host:$port HTTP/1.1\r\n" +
                        "Host: $host:$port\r\n" +
                        "Proxy-Connection: keep-alive\r\n\r\n"

                // 创建请求元数据
                val requestMetadata = mapOf(
                    "method" to "CONNECT",
                    "uri" to "$host:$port",
                    "host" to host,
                    "port" to port.toString(),
                    "protocol" to "HTTPS",
                    "headers" to request.headers().entries().joinToString("; ") { "${it.key}: ${it.value}" },
                    "timestamp" to System.currentTimeMillis().toString(),
                    "source" to ctx.channel().remoteAddress().toString(),
                    "destination" to "$host:$port"
                )

                // 创建数据包
                val connectPacket = PacketData.clientToServer(
                    data = connectContent.toByteArray(Charsets.UTF_8),
                    metadata = requestMetadata
                ).withParsedData("CONNECT Request: $host:$port")

                // 创建协议上下文
                val context = ProtocolContext.create(
                    protocol = Protocol.HTTPS,
                    source = ctx.channel().remoteAddress().toString(),
                    destination = "$host:$port",
                    sessionAttributes = mapOf(
                        "connect_request" to "true",
                        "ssl_enabled" to "false"
                    ),
                    contextMetadata = mapOf(
                        "request_type" to "connect",
                        "tunnel_mode" to "true"
                    )
                )

                // 通知回调
                callbackManager.notifyPacketReceived(connectPacket, context)

            } catch (e: Exception) {
                logger.warn("通知CONNECT请求失败: $host:$port", e)
            }
        }

        /**
         * 降级到隧道模式
         */
        private fun fallbackToTunnel(ctx: ChannelHandlerContext, host: String, port: Int) {
            try {
                logger.info("🔄 开始降级到隧道模式: $host:$port")

                // 检查连接是否还活跃
                if (!ctx.channel().isActive) {
                    logger.warn("⚠️ 连接已关闭，无法降级: $host:$port")
                    return
                }

                // 清理所有处理器，重新开始
                val pipeline = ctx.pipeline()

                // 安全地移除处理器
                listOf("httpsHandler", "httpAggregator", "httpCodec", "ssl").forEach { handlerName ->
                    try {
                        if (pipeline.get(handlerName) != null) {
                            logger.debug("移除处理器: $handlerName")
                            pipeline.remove(handlerName)
                        }
                    } catch (e: Exception) {
                        logger.debug("移除处理器失败: $handlerName - ${e.message}")
                    }
                }

                // 检查是否已经发送过CONNECT响应
                val connectResponseSent = ctx.channel().attr(AttributeKey.valueOf<Boolean>("connect_response_sent")).get()

                if (connectResponseSent != true) {
                    // 发送CONNECT成功响应
                    val response = "HTTP/1.1 200 Connection Established\r\n\r\n"
                    val responseBuffer = ctx.alloc().buffer()
                    responseBuffer.writeBytes(response.toByteArray(Charsets.UTF_8))

                    // 标记CONNECT响应已发送
                    ctx.channel().attr(AttributeKey.valueOf<Boolean>("connect_response_sent")).set(true)

                    ctx.writeAndFlush(responseBuffer).addListener { future ->
                        if (future.isSuccess) {
                            logger.debug("✅ CONNECT响应发送成功: $host:$port")
                            // 建立隧道连接
                            establishTunnelConnection(ctx, host, port)
                        } else {
                            logger.error("❌ CONNECT响应发送失败: $host:$port", future.cause())
                            ctx.close()
                        }
                    }
                } else {
                    logger.debug("CONNECT响应已发送，直接建立隧道连接: $host:$port")
                    establishTunnelConnection(ctx, host, port)
                }

            } catch (e: Exception) {
                logger.error("降级到隧道模式失败: $host:$port", e)
                ctx.close()
            }
        }

        /**
         * 建立隧道连接
         */
        private fun establishTunnelConnection(ctx: ChannelHandlerContext, host: String, port: Int) {
            try {
                logger.debug("🔗 建立隧道连接: $host:$port")

                // 连接到目标服务器
                val targetBootstrap = Bootstrap()
                    .group(ctx.channel().eventLoop())
                    .channel(NioSocketChannel::class.java)
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .handler(object : ChannelInitializer<SocketChannel>() {
                        override fun initChannel(ch: SocketChannel) {
                            ch.pipeline().addLast(RelayHandler(ctx.channel()))
                        }
                    })

                targetBootstrap.connect(host, port).addListener { connectFuture ->
                    if (connectFuture.isSuccess) {
                        val targetChannel = (connectFuture as ChannelFuture).channel()
                        logger.debug("✅ 隧道连接建立成功: $host:$port")

                        // 设置双向转发
                        ctx.pipeline().addLast(RelayHandler(targetChannel))

                    } else {
                        logger.error("❌ 隧道连接失败: $host:$port", connectFuture.cause())
                        ctx.close()
                    }
                }

            } catch (e: Exception) {
                logger.error("建立隧道连接失败: $host:$port", e)
                ctx.close()
            }
        }

        /**
         * 处理普通隧道连接
         */
        private fun handleTunnelConnect(ctx: ChannelHandlerContext, host: String, port: Int, silent: Boolean = false) {
            if (!silent) {
                logger.info("🔗 使用隧道模式: $host:$port")
            }

            // 创建到目标服务器的连接
            val bootstrap = Bootstrap()
            bootstrap.group(ctx.channel().eventLoop())
                .channel(NioSocketChannel::class.java)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(object : ChannelInitializer<SocketChannel>() {
                    override fun initChannel(ch: SocketChannel) {
                        ch.pipeline().addLast(RelayHandler(ctx.channel()))
                    }
                })

            val connectFuture = bootstrap.connect(host, port)
            connectFuture.addListener { future ->
                if (future.isSuccess) {
                    val targetChannel = connectFuture.channel()

                    // 连接成功，发送200 Connection Established
                    val response = DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.OK)
                    response.headers().set("Connection", "keep-alive")

                    ctx.writeAndFlush(response).addListener { writeFuture ->
                        if (writeFuture.isSuccess) {
                            // 移除HTTP编解码器，切换到原始数据转发模式
                            ctx.pipeline().remove("httpCodec")
                            ctx.pipeline().remove("httpAggregator")
                            ctx.pipeline().addLast(RelayHandler(targetChannel))

                            if (!silent) {
                                logger.info("✅ 隧道模式建立成功: $host:$port")
                            }
                        } else {
                            if (!silent) {
                                logger.error("❌ 发送CONNECT响应失败: $host:$port")
                            }
                            ctx.close()
                            targetChannel.close()
                        }
                    }
                } else {
                    logger.error("连接目标服务器失败: $host:$port", future.cause())
                    sendErrorResponse(ctx, HttpResponseStatus.BAD_GATEWAY, "Cannot connect to $host:$port")
                }
            }
        }

        /**
         * 处理HTTP请求
         */
        private fun handleHttpRequest(ctx: ChannelHandlerContext, request: FullHttpRequest) {
            val uri = request.uri()
            val host = extractHostFromUri(uri)

            // 移除忽略域名逻辑，所有域名都进行处理

            logger.info("📡 HTTP请求: ${request.method().name()} $uri")

            // 对于HTTP请求，我们可以解析并转发
            // 这里先返回一个简单的响应
            val response = DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1,
                HttpResponseStatus.OK,
                ctx.alloc().buffer().writeBytes("PacketCapture Proxy Server\nHTTP Request: ${request.method().name()} $uri".toByteArray())
            )

            response.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/plain")
            response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes())
            response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE)

            ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE)
            logger.info("✅ HTTP请求处理完成: ${request.method().name()} $uri")
        }

        /**
         * 从URI中提取主机名
         */
        private fun extractHostFromUri(uri: String): String {
            return try {
                if (uri.startsWith("http://") || uri.startsWith("https://")) {
                    val url = java.net.URL(uri)
                    url.host
                } else {
                    // 相对路径，无法提取主机名
                    ""
                }
            } catch (e: Exception) {
                ""
            }
        }

        /**
         * 发送错误响应
         */
        private fun sendErrorResponse(ctx: ChannelHandlerContext, status: HttpResponseStatus, message: String) {
            val response = DefaultFullHttpResponse(
                HttpVersion.HTTP_1_1,
                status,
                ctx.alloc().buffer().writeBytes(message.toByteArray())
            )

            response.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/plain")
            response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes())
            response.headers().set(HttpHeaderNames.CONNECTION, HttpHeaderValues.CLOSE)

            ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE)
            logger.warn("发送错误响应: $status - $message")
        }

        override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
            val remoteAddress = ctx.channel().remoteAddress()

            when {
                // SSL协议异常 - 触发降级
                cause is DecoderException && cause.cause is SSLProtocolException -> {
                    val sslCause = cause.cause as SSLProtocolException
                    logger.warn("🔄 SSL协议异常，触发降级: $remoteAddress - ${sslCause.message}")

                    // 尝试从channel属性中获取host和port信息
                    val host = ctx.channel().attr(AttributeKey.valueOf<String>("target_host")).get()
                    val port = ctx.channel().attr(AttributeKey.valueOf<Int>("target_port")).get()

                    if (host != null && port != null) {
                        logger.info("🔄 SSL异常触发降级到隧道模式: $host:$port")
                        fallbackToTunnel(ctx, host, port)
                        return
                    } else {
                        logger.warn("⚠️ 无法获取目标地址信息，关闭连接: $remoteAddress")
                    }
                }

                // SSL握手异常
                cause is SSLProtocolException -> {
                    logger.warn("🔄 SSL握手异常，触发降级: $remoteAddress - ${cause.message}")

                    val host = ctx.channel().attr(AttributeKey.valueOf<String>("target_host")).get()
                    val port = ctx.channel().attr(AttributeKey.valueOf<Int>("target_port")).get()

                    if (host != null && port != null) {
                        logger.info("🔄 SSL握手异常触发降级到隧道模式: $host:$port")
                        fallbackToTunnel(ctx, host, port)
                        return
                    }
                }

                // 连接重置
                cause.message?.contains("Connection reset") == true -> {
                    logger.debug("🔌 连接被重置: $remoteAddress")
                    ctx.close()
                    return
                }

                // 管道断开
                cause.message?.contains("Broken pipe") == true -> {
                    logger.debug("🔌 连接管道断开: $remoteAddress")
                    ctx.close()
                    return
                }

                // 其他SSL相关异常
                cause.message?.contains("SSL") == true || cause.message?.contains("TLS") == true -> {
                    logger.warn("🔄 SSL/TLS异常，尝试降级: $remoteAddress - ${cause.message}")

                    val host = ctx.channel().attr(AttributeKey.valueOf<String>("target_host")).get()
                    val port = ctx.channel().attr(AttributeKey.valueOf<Int>("target_port")).get()

                    if (host != null && port != null) {
                        fallbackToTunnel(ctx, host, port)
                        return
                    }
                }
            }

            logger.warn("处理请求时发生异常: $remoteAddress", cause)
            ctx.close()
        }
    }

    /**
     * HTTPS内容处理器
     * 处理解密后的HTTPS请求
     */
    private inner class HttpsContentHandler(
        private val targetHost: String,
        private val targetPort: Int
    ) : SimpleChannelInboundHandler<FullHttpRequest>() {

        override fun channelRead0(ctx: ChannelHandlerContext, request: FullHttpRequest) {
            val method = request.method().name()
            val uri = request.uri()
            val headers = request.headers()

            logger.info("🔓 解密HTTPS请求: $method https://$targetHost$uri")

            // 创建请求数据包并通过回调传递详细信息
            val requestContent = if (request.content().readableBytes() > 0) {
                request.content().toString(Charsets.UTF_8)
            } else {
                ""
            }

            // 构建请求元数据
            val requestMetadata = mutableMapOf<String, String>().apply {
                put("type", "request")
                put("method", method)
                put("uri", uri)
                put("host", targetHost)
                put("content_length", request.content().readableBytes().toString())

                // 添加请求头信息
                headers.forEach { header ->
                    put("header_${header.key.lowercase()}", header.value)
                }
            }

            // 创建数据包并通知回调
            val requestPacket = PacketData.clientToServer(
                data = requestContent.toByteArray(Charsets.UTF_8),
                metadata = requestMetadata
            ).withParsedData("HTTP Request: $method $uri")

            // 创建协议上下文
            val context = ProtocolContext.create(
                protocol = Protocol.HTTPS,
                source = ctx.channel().remoteAddress().toString(),
                destination = "$targetHost:443",
                sessionAttributes = mapOf("ssl_enabled" to "true"),
                contextMetadata = mapOf("request_type" to "https")
            )

            // 通知回调
            callbackManager.notifyPacketReceived(requestPacket, context)

            // 保留请求的引用，防止被释放
            request.retain()

            // 转发请求到目标服务器
            forwardHttpsRequest(ctx, request)
        }

        /**
         * 转发HTTPS请求到目标服务器
         */
        private fun forwardHttpsRequest(ctx: ChannelHandlerContext, request: FullHttpRequest) {
            // 创建到目标服务器的SSL连接
            val bootstrap = Bootstrap()
            bootstrap.group(ctx.channel().eventLoop())
                .channel(NioSocketChannel::class.java)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 10000)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(object : ChannelInitializer<SocketChannel>() {
                    override fun initChannel(ch: SocketChannel) {
                        val pipeline = ch.pipeline()

                        // 添加SSL处理器（客户端模式）
                        val sslHandler = clientSslContext!!.newHandler(ch.alloc(), targetHost, targetPort)
                        pipeline.addLast("ssl", sslHandler)

                        // 添加HTTP编解码器
                        pipeline.addLast("httpCodec", HttpClientCodec())
                        pipeline.addLast("httpAggregator", HttpObjectAggregator(65536))

                        // 添加响应处理器
                        pipeline.addLast("responseHandler", HttpsResponseHandler(ctx))
                    }
                })

            val connectFuture = bootstrap.connect(targetHost, targetPort)
            connectFuture.addListener { future ->
                if (future.isSuccess) {
                    val targetChannel = connectFuture.channel()

                    // 创建请求内容的副本
                    val contentCopy = ctx.alloc().buffer(request.content().readableBytes())
                    contentCopy.writeBytes(request.content(), request.content().readerIndex(), request.content().readableBytes())

                    // 创建新的请求对象
                    val modifiedRequest = DefaultFullHttpRequest(
                        request.protocolVersion(),
                        request.method(),
                        request.uri(), // 保持原始URI
                        contentCopy
                    )

                    // 复制所有请求头
                    request.headers().forEach { header ->
                        modifiedRequest.headers().set(header.key, header.value)
                    }

                    // 发送请求到目标服务器
                    targetChannel.writeAndFlush(modifiedRequest).addListener { writeResult ->
                        if (writeResult.isSuccess) {
                            logger.debug("✅ HTTPS请求已转发到目标服务器: $targetHost:$targetPort")
                        } else {
                            logger.error("❌ 转发HTTPS请求失败: $targetHost:$targetPort", writeResult.cause())
                            ctx.close()
                            targetChannel.close()
                        }

                        // 释放原始请求的引用
                        request.release()
                    }
                } else {
                    logger.error("❌ 连接目标服务器失败: $targetHost:$targetPort", future.cause())

                    // 发送错误响应给客户端
                    val errorResponse = DefaultFullHttpResponse(
                        HttpVersion.HTTP_1_1,
                        HttpResponseStatus.BAD_GATEWAY,
                        ctx.alloc().buffer().writeBytes("Cannot connect to target server".toByteArray())
                    )
                    errorResponse.headers().set(HttpHeaderNames.CONTENT_TYPE, "text/plain")
                    errorResponse.headers().set(HttpHeaderNames.CONTENT_LENGTH, errorResponse.content().readableBytes())
                    ctx.writeAndFlush(errorResponse).addListener(ChannelFutureListener.CLOSE)
                }
            }
        }

        override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
            when {
                cause.message?.contains("certificate_unknown") == true -> {
                    logger.error("🚫 SSL证书被拒绝 - 域名: $targetHost:$targetPort")
                    logger.error("💡 证书问题诊断:")
                    logger.error("   1. 检查设备是否已安装根证书 (root-ca.pem)")
                    logger.error("   2. 检查证书是否已添加到系统信任列表")
                    logger.error("   3. 检查应用是否信任用户证书")
                    logger.debug("SSL握手失败详情", cause)
                }
                cause.message?.contains("SSLHandshakeException") == true -> {
                    logger.error("🔒 SSL握手失败 - 域名: $targetHost:$targetPort")
                    logger.error("🔍 可能的原因:")
                    logger.error("   1. 证书主题名称不匹配")
                    logger.error("   2. 证书链验证失败")
                    logger.error("   3. SSL协议版本不兼容")
                    logger.debug("SSL握手失败详情", cause)
                }
                cause.message?.contains("SSLException") == true -> {
                    logger.error("� SSL连接异常 - 域名: $targetHost:$targetPort")
                    logger.debug("SSL异常详情", cause)
                }
                else -> {
                    logger.error("⚠️ HTTPS内容处理异常 - 域名: $targetHost:$targetPort", cause)
                }
            }

            logger.info("🔄 SSL解密失败，建议检查证书安装")
            ctx.close()
        }
    }

    /**
     * HTTPS响应处理器
     */
    private inner class HttpsResponseHandler(private val clientCtx: ChannelHandlerContext) : SimpleChannelInboundHandler<FullHttpResponse>() {

        private val logger = LoggerFactory.getLogger(HttpsResponseHandler::class.java)

        override fun channelRead0(ctx: ChannelHandlerContext, response: FullHttpResponse) {
            val status = response.status()
            val headers = response.headers()

            logger.info("🔓 解密HTTPS响应: ${status.code()} ${status.reasonPhrase()}")

            // 创建响应数据包并通过回调传递详细信息
            val responseContent = if (response.content().readableBytes() > 0) {
                response.content().toString(Charsets.UTF_8)
            } else {
                ""
            }

            // 构建响应元数据
            val responseMetadata = mutableMapOf<String, String>().apply {
                put("type", "response")
                put("status_code", status.code().toString())
                put("status_message", status.reasonPhrase())
                put("content_length", response.content().readableBytes().toString())

                // 添加响应头信息
                headers.forEach { header ->
                    put("header_${header.key.lowercase()}", header.value)
                }
            }

            // 创建数据包并通知回调
            val responsePacket = PacketData.serverToClient(
                data = responseContent.toByteArray(Charsets.UTF_8),
                metadata = responseMetadata
            ).withParsedData("HTTP Response: ${status.code()} ${status.reasonPhrase()}")

            // 创建协议上下文
            val context = ProtocolContext.create(
                protocol = Protocol.HTTPS,
                source = ctx.channel().remoteAddress().toString(),
                destination = "server:443",
                sessionAttributes = mapOf("ssl_enabled" to "true"),
                contextMetadata = mapOf("response_type" to "https")
            )

            // 通知回调
            callbackManager.notifyPacketReceived(responsePacket, context)

            // 创建响应内容的副本
            val contentCopy = clientCtx.alloc().buffer(response.content().readableBytes())
            contentCopy.writeBytes(response.content(), response.content().readerIndex(), response.content().readableBytes())

            // 创建响应副本并转发给客户端
            val responseToClient = DefaultFullHttpResponse(
                response.protocolVersion(),
                response.status(),
                contentCopy
            )

            // 复制所有响应头
            response.headers().forEach { header ->
                responseToClient.headers().set(header.key, header.value)
            }

            // 转发响应给客户端
            clientCtx.writeAndFlush(responseToClient).addListener { writeResult ->
                if (writeResult.isSuccess) {
                    logger.debug("✅ HTTPS响应已转发给客户端")
                } else {
                    logger.error("❌ 转发HTTPS响应失败", writeResult.cause())
                }
            }
        }

        override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
            logger.warn("HTTPS响应处理异常", cause)
            ctx.close()
            clientCtx.close()
        }
    }

    /**
     * 数据转发处理器
     * 用于在客户端和目标服务器之间转发原始数据
     */
    private class RelayHandler(private val relayChannel: Channel) : ChannelInboundHandlerAdapter() {

        private val logger = LoggerFactory.getLogger(RelayHandler::class.java)

        override fun channelActive(ctx: ChannelHandlerContext) {
            logger.debug("转发通道激活: ${ctx.channel().remoteAddress()} <-> ${relayChannel.remoteAddress()}")
            super.channelActive(ctx)
        }

        override fun channelRead(ctx: ChannelHandlerContext, msg: Any) {
            if (relayChannel.isActive) {
                if (msg is ByteBuf) {
                    val dataSize = msg.readableBytes()
                    logger.trace("转发数据: ${dataSize}字节 从 ${ctx.channel().remoteAddress()} 到 ${relayChannel.remoteAddress()}")
                }
                relayChannel.writeAndFlush(msg)
            } else {
                // 如果目标通道已关闭，释放消息并关闭当前通道
                if (msg is ByteBuf) {
                    msg.release()
                }
                ctx.close()
            }
        }

        override fun channelInactive(ctx: ChannelHandlerContext) {
            logger.debug("转发通道断开: ${ctx.channel().remoteAddress()}")
            if (relayChannel.isActive) {
                relayChannel.close()
            }
            super.channelInactive(ctx)
        }

        override fun exceptionCaught(ctx: ChannelHandlerContext, cause: Throwable) {
            logger.debug("转发通道异常: ${ctx.channel().remoteAddress()}", cause)
            ctx.close()
            if (relayChannel.isActive) {
                relayChannel.close()
            }
        }
    }
}
