package com.dev.tools.capture.mock

import com.dev.tools.capture.exception.MockProcessingException
import com.dev.tools.capture.exception.MockRuleInvalidException
import com.dev.tools.capture.model.MockRule
import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.ProtocolContext
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Mock引擎
 * 
 * 负责管理Mock规则并处理数据包匹配和响应生成
 */
class MockEngine {
    
    private val logger = LoggerFactory.getLogger(MockEngine::class.java)
    
    // 规则存储：ID -> 规则
    private val rules = ConcurrentHashMap<String, MockRule>()
    
    // 按优先级排序的规则列表
    private val sortedRules = CopyOnWriteArrayList<MockRule>()
    
    // 规则统计
    private val ruleStats = ConcurrentHashMap<String, RuleStats>()
    
    /**
     * 添加Mock规则
     * 
     * @param rule Mock规则
     * @throws MockRuleInvalidException 如果规则无效
     */
    @Throws(MockRuleInvalidException::class)
    fun addRule(rule: MockRule) {
        validateRule(rule)
        
        // 检查ID是否重复
        if (rules.containsKey(rule.id)) {
            val existingRule = rules[rule.id]!!
            throw MockRuleInvalidException.duplicateId(rule.id, existingRule.name)
        }
        
        rules[rule.id] = rule
        ruleStats[rule.id] = RuleStats()
        
        // 重新排序规则列表
        updateSortedRules()
        
        logger.info("添加Mock规则: ${rule.name} (${rule.id}), 优先级: ${rule.priority}")
    }
    
    /**
     * 移除Mock规则
     * 
     * @param ruleId 规则ID
     * @return 是否成功移除
     */
    fun removeRule(ruleId: String): Boolean {
        val rule = rules.remove(ruleId)
        return if (rule != null) {
            ruleStats.remove(ruleId)
            updateSortedRules()
            logger.info("移除Mock规则: ${rule.name} (${rule.id})")
            true
        } else {
            false
        }
    }
    
    /**
     * 获取所有规则
     */
    fun getAllRules(): List<MockRule> {
        return sortedRules.toList()
    }
    
    /**
     * 获取规则数量
     */
    fun getRuleCount(): Int = rules.size
    
    /**
     * 启用/禁用规则
     * 
     * @param ruleId 规则ID
     * @param enabled 是否启用
     * @return 是否成功更新
     */
    fun setRuleEnabled(ruleId: String, enabled: Boolean): Boolean {
        val rule = rules[ruleId]
        return if (rule != null) {
            val updatedRule = rule.update(enabled = enabled)
            rules[ruleId] = updatedRule
            updateSortedRules()
            logger.info("更新规则状态: ${rule.name} (${rule.id}), 启用: $enabled")
            true
        } else {
            false
        }
    }
    
    /**
     * 获取规则统计信息
     * 
     * @param ruleId 规则ID
     * @return 统计信息，如果规则不存在则返回null
     */
    fun getRuleStats(ruleId: String): RuleStats? {
        return ruleStats[ruleId]?.copy()
    }
    
    /**
     * 处理数据包，检查是否匹配Mock规则
     * 
     * @param packet 数据包
     * @param context 协议上下文
     * @return Mock响应数据包，如果没有匹配的规则则返回null
     */
    fun processPacket(packet: PacketData, context: ProtocolContext): MockResult? {
        if (sortedRules.isEmpty()) {
            return null
        }
        
        // 按优先级顺序检查规则
        for (rule in sortedRules) {
            if (!rule.enabled) {
                continue
            }
            
            val stats = ruleStats[rule.id] ?: continue
            stats.evaluationCount++
            
            try {
                if (rule.matches(packet, context)) {
                    stats.matchCount++
                    
                    try {
                        val mockResponse = rule.generateResponse(packet, context)
                        stats.successCount++
                        
                        logger.debug("Mock规则匹配: ${rule.name} (${rule.id})")
                        
                        return MockResult(
                            rule = rule,
                            originalPacket = packet,
                            mockResponse = mockResponse,
                            context = context
                        )
                        
                    } catch (e: Exception) {
                        stats.errorCount++
                        logger.warn("Mock响应生成失败: ${rule.name} (${rule.id})", e)
                        
                        throw MockProcessingException.responseGenerationFailed(
                            ruleId = rule.id,
                            ruleName = rule.name,
                            context = context,
                            cause = e
                        )
                    }
                }
                
            } catch (e: MockProcessingException) {
                // 重新抛出Mock异常
                throw e
            } catch (e: Exception) {
                stats.errorCount++
                logger.warn("Mock规则条件评估失败: ${rule.name} (${rule.id})", e)
                
                throw MockProcessingException.conditionEvaluationFailed(
                    ruleId = rule.id,
                    ruleName = rule.name,
                    context = context,
                    cause = e
                )
            }
        }
        
        return null
    }
    
    /**
     * 验证规则有效性
     */
    private fun validateRule(rule: MockRule) {
        if (rule.name.isBlank()) {
            throw MockRuleInvalidException(
                ruleId = rule.id,
                ruleName = rule.name,
                reason = "规则名称不能为空"
            )
        }
        
        // 可以添加更多验证逻辑
        try {
            // 测试条件函数是否可调用（使用空数据）
            val testPacket = PacketData.fromString("")
            val testContext = ProtocolContext.create(
                protocol = com.dev.tools.capture.model.Protocol.HTTP,
                source = "test",
                destination = "test"
            )
            
            rule.condition(testPacket, testContext)
        } catch (e: Exception) {
            throw MockRuleInvalidException.missingCondition(rule.id, rule.name)
        }
    }
    
    /**
     * 更新排序后的规则列表
     */
    private fun updateSortedRules() {
        sortedRules.clear()
        sortedRules.addAll(
            rules.values.sortedByDescending { it.priority }
        )
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        rules.clear()
        sortedRules.clear()
        ruleStats.clear()
        logger.info("Mock引擎已清理")
    }
}

/**
 * Mock处理结果
 */
data class MockResult(
    val rule: MockRule,
    val originalPacket: PacketData,
    val mockResponse: PacketData,
    val context: ProtocolContext
)

/**
 * 规则统计信息
 */
data class RuleStats(
    var evaluationCount: Long = 0,  // 评估次数
    var matchCount: Long = 0,       // 匹配次数
    var successCount: Long = 0,     // 成功响应次数
    var errorCount: Long = 0        // 错误次数
) {
    /**
     * 匹配率
     */
    val matchRate: Double
        get() = if (evaluationCount > 0) matchCount.toDouble() / evaluationCount else 0.0
    
    /**
     * 成功率
     */
    val successRate: Double
        get() = if (matchCount > 0) successCount.toDouble() / matchCount else 0.0
    
    /**
     * 错误率
     */
    val errorRate: Double
        get() = if (evaluationCount > 0) errorCount.toDouble() / evaluationCount else 0.0
    
    fun copy(): RuleStats {
        return RuleStats(evaluationCount, matchCount, successCount, errorCount)
    }
}
