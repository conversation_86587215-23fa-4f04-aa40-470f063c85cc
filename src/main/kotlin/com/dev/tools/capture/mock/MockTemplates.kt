package com.dev.tools.capture.mock

import com.dev.tools.capture.model.MockRule
import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext

/**
 * Mock规则模板
 * 
 * 提供常用的Mock规则模板
 */
object MockTemplates {
    
    /**
     * 用户认证相关的Mock规则
     */
    object Auth {
        
        /**
         * 登录成功Mock
         */
        fun loginSuccess(
            username: String = "testuser",
            token: String = "mock_token_${System.currentTimeMillis()}"
        ): MockRule {
            return MockRuleBuilder.create("登录成功Mock")
                .description("模拟用户登录成功")
                .matchHttpMethod("POST")
                .matchUrlPattern(".*/login.*")
                .respondWithJson("""
                    {
                        "success": true,
                        "message": "登录成功",
                        "data": {
                            "username": "$username",
                            "token": "$token",
                            "expires_in": 3600
                        }
                    }
                """.trimIndent())
                .build()
        }
        
        /**
         * 登录失败Mock
         */
        fun loginFailure(errorMessage: String = "用户名或密码错误"): MockRule {
            return MockRuleBuilder.create("登录失败Mock")
                .description("模拟用户登录失败")
                .matchHttpMethod("POST")
                .matchUrlPattern(".*/login.*")
                .respondWithJson("""
                    {
                        "success": false,
                        "message": "$errorMessage",
                        "error_code": "AUTH_FAILED"
                    }
                """.trimIndent(), 401)
                .build()
        }
        
        /**
         * Token验证Mock
         */
        fun tokenValidation(isValid: Boolean = true): MockRule {
            return if (isValid) {
                MockRuleBuilder.create("Token验证成功Mock")
                    .description("模拟Token验证成功")
                    .matchHttpHeader("Authorization", "Bearer")
                    .respondWithJson("""
                        {
                            "valid": true,
                            "user": {
                                "id": 1,
                                "username": "testuser",
                                "role": "user"
                            }
                        }
                    """.trimIndent())
                    .build()
            } else {
                MockRuleBuilder.create("Token验证失败Mock")
                    .description("模拟Token验证失败")
                    .matchHttpHeader("Authorization", "Bearer")
                    .respondWithJson("""
                        {
                            "valid": false,
                            "message": "Token已过期或无效"
                        }
                    """.trimIndent(), 401)
                    .build()
            }
        }
    }
    
    /**
     * API相关的Mock规则
     */
    object Api {
        
        /**
         * 用户列表Mock
         */
        fun userList(): MockRule {
            return MockRuleBuilder.create("用户列表Mock")
                .description("模拟获取用户列表")
                .matchHttpMethod("GET")
                .matchUrlPattern(".*/users.*")
                .respondWithJson("""
                    {
                        "success": true,
                        "data": [
                            {
                                "id": 1,
                                "username": "user1",
                                "email": "<EMAIL>",
                                "created_at": "2024-01-01T00:00:00Z"
                            },
                            {
                                "id": 2,
                                "username": "user2",
                                "email": "<EMAIL>",
                                "created_at": "2024-01-02T00:00:00Z"
                            }
                        ],
                        "total": 2,
                        "page": 1,
                        "per_page": 10
                    }
                """.trimIndent())
                .build()
        }
        
        /**
         * 创建用户Mock
         */
        fun createUser(): MockRule {
            return MockRuleBuilder.create("创建用户Mock")
                .description("模拟创建用户")
                .matchHttpMethod("POST")
                .matchUrlPattern(".*/users.*")
                .respondWithJson("""
                    {
                        "success": true,
                        "message": "用户创建成功",
                        "data": {
                            "id": ${System.currentTimeMillis() % 10000},
                            "username": "newuser",
                            "email": "<EMAIL>",
                            "created_at": "${java.time.Instant.now()}"
                        }
                    }
                """.trimIndent(), 201)
                .build()
        }
        
        /**
         * 服务器错误Mock
         */
        fun serverError(): MockRule {
            return MockRuleBuilder.create("服务器错误Mock")
                .description("模拟服务器内部错误")
                .matchUrlPattern(".*/error.*")
                .respondWithError(500, """
                    {
                        "success": false,
                        "message": "服务器内部错误",
                        "error_code": "INTERNAL_ERROR"
                    }
                """.trimIndent(), "application/json")
                .build()
        }
        
        /**
         * 慢响应Mock
         */
        fun slowResponse(delayMs: Long = 5000): MockRule {
            return MockRuleBuilder.create("慢响应Mock")
                .description("模拟慢响应")
                .matchUrlPattern(".*/slow.*")
                .respondWithJson("""
                    {
                        "success": true,
                        "message": "这是一个慢响应",
                        "delay_ms": $delayMs
                    }
                """.trimIndent())
                .withDelay(delayMs)
                .build()
        }
    }
    
    /**
     * 文件相关的Mock规则
     */
    object Files {
        
        /**
         * HTML页面Mock
         */
        fun htmlPage(title: String = "Mock页面", content: String = "这是一个Mock页面"): MockRule {
            return MockRuleBuilder.create("HTML页面Mock")
                .description("模拟HTML页面")
                .matchUrlPattern(".*\\.(html|htm).*")
                .respondWithFile("""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>$title</title>
                        <meta charset="UTF-8">
                    </head>
                    <body>
                        <h1>$title</h1>
                        <p>$content</p>
                        <p>这是由PacketCapture SDK生成的Mock页面</p>
                    </body>
                    </html>
                """.trimIndent(), "text/html")
                .build()
        }
        
        /**
         * CSS文件Mock
         */
        fun cssFile(): MockRule {
            return MockRuleBuilder.create("CSS文件Mock")
                .description("模拟CSS文件")
                .matchUrlPattern(".*\\.css.*")
                .respondWithFile("""
                    body {
                        font-family: Arial, sans-serif;
                        margin: 20px;
                        background-color: #f5f5f5;
                    }
                    
                    h1 {
                        color: #333;
                        border-bottom: 2px solid #007acc;
                    }
                    
                    p {
                        line-height: 1.6;
                        color: #666;
                    }
                """.trimIndent(), "text/css")
                .build()
        }
        
        /**
         * JavaScript文件Mock
         */
        fun jsFile(): MockRule {
            return MockRuleBuilder.create("JavaScript文件Mock")
                .description("模拟JavaScript文件")
                .matchUrlPattern(".*\\.js.*")
                .respondWithFile("""
                    console.log('这是由PacketCapture SDK生成的Mock JavaScript文件');
                    
                    function mockFunction() {
                        return {
                            message: 'Hello from Mock!',
                            timestamp: new Date().toISOString()
                        };
                    }
                    
                    // 模拟一些常用的全局变量
                    window.MOCK_DATA = {
                        version: '1.0.0',
                        environment: 'mock'
                    };
                """.trimIndent(), "application/javascript")
                .build()
        }
        
        /**
         * 图片文件Mock（返回1x1像素的透明PNG）
         */
        fun imageFile(): MockRule {
            return MockRuleBuilder.create("图片文件Mock")
                .description("模拟图片文件")
                .matchUrlPattern(".*\\.(png|jpg|jpeg|gif|svg).*")
                .respondWith { _, _ ->
                    // 1x1像素透明PNG的base64编码
                    val pngData = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                    val binaryData = java.util.Base64.getDecoder().decode(pngData)
                    
                    PacketData(
                        rawData = "HTTP/1.1 200 OK\r\nContent-Type: image/png\r\nContent-Length: ${binaryData.size}\r\n\r\n".toByteArray() + binaryData,
                        direction = PacketData.PacketDirection.SERVER_TO_CLIENT,
                        metadata = mapOf("mock" to "true", "content_type" to "image/png")
                    )
                }
                .build()
        }
    }
    
    /**
     * WebSocket相关的Mock规则
     */
    object WebSocket {
        
        /**
         * WebSocket握手成功Mock
         */
        fun handshakeSuccess(): MockRule {
            return MockRuleBuilder.create("WebSocket握手成功Mock")
                .description("模拟WebSocket握手成功")
                .matchProtocol(Protocol.WEBSOCKET)
                .matchContent("Upgrade: websocket")
                .respondWith { packet, context ->
                    val key = extractWebSocketKey(packet.rawDataAsString)
                    val accept = generateWebSocketAccept(key)
                    
                    val response = """
                        HTTP/1.1 101 Switching Protocols
                        Upgrade: websocket
                        Connection: Upgrade
                        Sec-WebSocket-Accept: $accept
                        
                    """.trimIndent().replace("\n", "\r\n")
                    
                    PacketData.fromString(
                        response,
                        PacketData.PacketDirection.SERVER_TO_CLIENT,
                        mapOf("mock" to "true", "websocket_handshake" to "success")
                    )
                }
                .build()
        }
        
        /**
         * WebSocket消息Mock
         */
        fun message(message: String): MockRule {
            return MockRuleBuilder.create("WebSocket消息Mock")
                .description("模拟WebSocket消息")
                .matchProtocol(Protocol.WEBSOCKET)
                .respondWithWebSocket(message)
                .build()
        }
        
        private fun extractWebSocketKey(request: String): String {
            val lines = request.split("\r\n", "\n")
            for (line in lines) {
                if (line.startsWith("Sec-WebSocket-Key:", ignoreCase = true)) {
                    return line.substring(18).trim()
                }
            }
            return ""
        }
        
        private fun generateWebSocketAccept(key: String): String {
            val magicString = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"
            val combined = key + magicString
            val digest = java.security.MessageDigest.getInstance("SHA-1")
            val hash = digest.digest(combined.toByteArray())
            return java.util.Base64.getEncoder().encodeToString(hash)
        }
    }
    
    /**
     * 获取所有预定义的Mock规则模板
     */
    fun getAllTemplates(): List<MockRule> {
        return listOf(
            Auth.loginSuccess(),
            Auth.tokenValidation(),
            Api.userList(),
            Api.createUser(),
            Files.htmlPage(),
            Files.cssFile(),
            Files.jsFile(),
            Files.imageFile(),
            WebSocket.handshakeSuccess()
        )
    }
    
    /**
     * 根据名称获取模板
     */
    fun getTemplate(name: String): MockRule? {
        return getAllTemplates().find { it.name == name }
    }
}
