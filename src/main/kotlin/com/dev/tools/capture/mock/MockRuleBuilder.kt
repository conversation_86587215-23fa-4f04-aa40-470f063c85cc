package com.dev.tools.capture.mock

import com.dev.tools.capture.model.MockRule
import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import java.util.regex.Pattern

/**
 * Mock规则构建器
 * 
 * 提供流式API来构建复杂的Mock规则
 */
class MockRuleBuilder(private val name: String) {
    
    private var description: String = ""
    private var enabled: Boolean = true
    private var priority: Int = 0
    private var conditions = mutableListOf<(PacketData, ProtocolContext) -> Boolean>()
    private var responseGenerator: ((PacketData, ProtocolContext) -> PacketData)? = null
    private var metadata = mutableMapOf<String, String>()
    
    /**
     * 设置规则描述
     */
    fun description(desc: String): MockRuleBuilder {
        this.description = desc
        return this
    }
    
    /**
     * 设置规则优先级
     */
    fun priority(pri: Int): MockRuleBuilder {
        this.priority = pri
        return this
    }
    
    /**
     * 设置规则是否启用
     */
    fun enabled(enable: Boolean): MockRuleBuilder {
        this.enabled = enable
        return this
    }
    
    /**
     * 添加元数据
     */
    fun metadata(key: String, value: String): MockRuleBuilder {
        this.metadata[key] = value
        return this
    }
    
    /**
     * 添加协议匹配条件
     */
    fun matchProtocol(protocol: Protocol): MockRuleBuilder {
        conditions.add { _, context -> context.protocol == protocol }
        return this
    }
    
    /**
     * 添加协议匹配条件（多个协议）
     */
    fun matchProtocols(vararg protocols: Protocol): MockRuleBuilder {
        val protocolSet = protocols.toSet()
        conditions.add { _, context -> context.protocol in protocolSet }
        return this
    }
    
    /**
     * 添加URL匹配条件（精确匹配）
     */
    fun matchUrl(url: String): MockRuleBuilder {
        conditions.add { packet, context ->
            context.protocol in listOf(Protocol.HTTP, Protocol.HTTPS) &&
            packet.rawDataAsString.contains(url)
        }
        return this
    }
    
    /**
     * 添加URL正则匹配条件
     */
    fun matchUrlPattern(pattern: String): MockRuleBuilder {
        val regex = Pattern.compile(pattern)
        conditions.add { packet, context ->
            context.protocol in listOf(Protocol.HTTP, Protocol.HTTPS) &&
            regex.matcher(packet.rawDataAsString).find()
        }
        return this
    }
    
    /**
     * 添加HTTP方法匹配条件
     */
    fun matchHttpMethod(method: String): MockRuleBuilder {
        conditions.add { packet, context ->
            context.protocol in listOf(Protocol.HTTP, Protocol.HTTPS) &&
            packet.rawDataAsString.startsWith("$method ")
        }
        return this
    }
    
    /**
     * 添加HTTP头部匹配条件
     */
    fun matchHttpHeader(headerName: String, headerValue: String): MockRuleBuilder {
        conditions.add { packet, context ->
            context.protocol in listOf(Protocol.HTTP, Protocol.HTTPS) &&
            packet.rawDataAsString.contains("$headerName: $headerValue", ignoreCase = true)
        }
        return this
    }
    
    /**
     * 添加内容匹配条件
     */
    fun matchContent(content: String, ignoreCase: Boolean = true): MockRuleBuilder {
        conditions.add { packet, _ ->
            packet.rawDataAsString.contains(content, ignoreCase)
        }
        return this
    }
    
    /**
     * 添加内容正则匹配条件
     */
    fun matchContentPattern(pattern: String): MockRuleBuilder {
        val regex = Pattern.compile(pattern)
        conditions.add { packet, _ ->
            regex.matcher(packet.rawDataAsString).find()
        }
        return this
    }
    
    /**
     * 添加数据包大小匹配条件
     */
    fun matchSize(minSize: Int, maxSize: Int = Int.MAX_VALUE): MockRuleBuilder {
        conditions.add { packet, _ ->
            packet.size in minSize..maxSize
        }
        return this
    }
    
    /**
     * 添加数据包方向匹配条件
     */
    fun matchDirection(direction: PacketData.PacketDirection): MockRuleBuilder {
        conditions.add { packet, _ ->
            packet.direction == direction
        }
        return this
    }
    
    /**
     * 添加会话属性匹配条件
     */
    fun matchSessionAttribute(key: String, value: String): MockRuleBuilder {
        conditions.add { _, context ->
            context.session.attributes[key] == value
        }
        return this
    }
    
    /**
     * 添加自定义条件
     */
    fun matchCustom(condition: (PacketData, ProtocolContext) -> Boolean): MockRuleBuilder {
        conditions.add(condition)
        return this
    }
    
    /**
     * 设置HTTP响应
     */
    fun respondWithHttp(
        statusCode: Int = 200,
        statusText: String = "OK",
        headers: Map<String, String> = emptyMap(),
        body: String = ""
    ): MockRuleBuilder {
        responseGenerator = { _, _ ->
            val responseHeaders = headers.entries.joinToString("\r\n") { "${it.key}: ${it.value}" }
            val httpResponse = buildString {
                append("HTTP/1.1 $statusCode $statusText\r\n")
                append("Content-Type: application/json\r\n")
                append("Content-Length: ${body.length}\r\n")
                if (responseHeaders.isNotEmpty()) {
                    append("$responseHeaders\r\n")
                }
                append("\r\n")
                append(body)
            }
            
            PacketData.fromString(
                httpResponse,
                PacketData.PacketDirection.SERVER_TO_CLIENT,
                mapOf("mock" to "true", "rule" to name)
            )
        }
        return this
    }
    
    /**
     * 设置JSON响应
     */
    fun respondWithJson(
        json: String,
        statusCode: Int = 200,
        additionalHeaders: Map<String, String> = emptyMap()
    ): MockRuleBuilder {
        val headers = mutableMapOf(
            "Content-Type" to "application/json",
            "Content-Length" to json.length.toString()
        )
        headers.putAll(additionalHeaders)
        
        return respondWithHttp(statusCode, "OK", headers, json)
    }
    
    /**
     * 设置文件响应
     */
    fun respondWithFile(
        content: String,
        contentType: String = "text/plain",
        statusCode: Int = 200
    ): MockRuleBuilder {
        val headers = mapOf(
            "Content-Type" to contentType,
            "Content-Length" to content.length.toString()
        )
        
        return respondWithHttp(statusCode, "OK", headers, content)
    }
    
    /**
     * 设置重定向响应
     */
    fun respondWithRedirect(location: String, statusCode: Int = 302): MockRuleBuilder {
        val headers = mapOf("Location" to location)
        return respondWithHttp(statusCode, "Found", headers, "")
    }
    
    /**
     * 设置错误响应
     */
    fun respondWithError(
        statusCode: Int,
        errorMessage: String = "",
        contentType: String = "text/plain"
    ): MockRuleBuilder {
        val statusText = when (statusCode) {
            400 -> "Bad Request"
            401 -> "Unauthorized"
            403 -> "Forbidden"
            404 -> "Not Found"
            500 -> "Internal Server Error"
            502 -> "Bad Gateway"
            503 -> "Service Unavailable"
            else -> "Error"
        }
        
        val headers = if (errorMessage.isNotEmpty()) {
            mapOf(
                "Content-Type" to contentType,
                "Content-Length" to errorMessage.length.toString()
            )
        } else {
            emptyMap()
        }
        
        return respondWithHttp(statusCode, statusText, headers, errorMessage)
    }
    
    /**
     * 设置WebSocket响应
     */
    fun respondWithWebSocket(message: String): MockRuleBuilder {
        responseGenerator = { _, _ ->
            // 简化的WebSocket文本帧
            val frame = ByteArray(2 + message.length)
            frame[0] = 0x81.toByte() // FIN=1, Opcode=1 (text)
            frame[1] = message.length.toByte() // 载荷长度
            System.arraycopy(message.toByteArray(), 0, frame, 2, message.length)
            
            PacketData(
                rawData = frame,
                direction = PacketData.PacketDirection.SERVER_TO_CLIENT,
                metadata = mapOf("mock" to "true", "rule" to name, "websocket_frame" to "text")
            )
        }
        return this
    }
    
    /**
     * 设置自定义响应生成器
     */
    fun respondWith(generator: (PacketData, ProtocolContext) -> PacketData): MockRuleBuilder {
        this.responseGenerator = generator
        return this
    }
    
    /**
     * 设置延迟响应
     */
    fun withDelay(delayMs: Long): MockRuleBuilder {
        val originalGenerator = responseGenerator
        responseGenerator = { packet, context ->
            Thread.sleep(delayMs)
            originalGenerator?.invoke(packet, context) ?: PacketData.fromString("")
        }
        metadata["delay_ms"] = delayMs.toString()
        return this
    }
    
    /**
     * 构建Mock规则
     */
    fun build(): MockRule {
        require(conditions.isNotEmpty()) { "至少需要一个匹配条件" }
        require(responseGenerator != null) { "需要设置响应生成器" }
        
        // 组合所有条件
        val combinedCondition: (PacketData, ProtocolContext) -> Boolean = { packet, context ->
            conditions.all { it(packet, context) }
        }
        
        return MockRule(
            name = name,
            description = description,
            enabled = enabled,
            priority = priority,
            condition = combinedCondition,
            response = responseGenerator!!,
            metadata = metadata.toMap()
        )
    }
    
    companion object {
        /**
         * 创建新的规则构建器
         */
        fun create(name: String): MockRuleBuilder {
            return MockRuleBuilder(name)
        }
        
        /**
         * 快速创建HTTP API Mock规则
         */
        fun httpApi(
            name: String,
            method: String,
            urlPattern: String,
            responseJson: String,
            statusCode: Int = 200
        ): MockRule {
            return create(name)
                .description("HTTP API Mock: $method $urlPattern")
                .matchHttpMethod(method)
                .matchUrlPattern(urlPattern)
                .respondWithJson(responseJson, statusCode)
                .build()
        }
        
        /**
         * 快速创建静态文件Mock规则
         */
        fun staticFile(
            name: String,
            urlPattern: String,
            content: String,
            contentType: String = "text/html"
        ): MockRule {
            return create(name)
                .description("静态文件Mock: $urlPattern")
                .matchUrlPattern(urlPattern)
                .respondWithFile(content, contentType)
                .build()
        }
        
        /**
         * 快速创建错误Mock规则
         */
        fun error(
            name: String,
            urlPattern: String,
            statusCode: Int,
            errorMessage: String = ""
        ): MockRule {
            return create(name)
                .description("错误Mock: $urlPattern -> $statusCode")
                .matchUrlPattern(urlPattern)
                .respondWithError(statusCode, errorMessage)
                .build()
        }
    }
}
