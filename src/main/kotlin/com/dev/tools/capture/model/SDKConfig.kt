package com.dev.tools.capture.model

import kotlinx.serialization.Serializable

/**
 * SDK配置
 *
 * @property port 监听端口
 * @property protocols 支持的协议集合
 * @property enableSslMitm 是否启用SSL中间人解密
 * @property sslFallbackStrategy SSL失败时的降级策略
 * @property sslCertificatePath SSL证书存储路径
 * @property sslKeyStorePath SSL密钥库路径
 * @property sslKeyStorePassword SSL密钥库密码
 * @property enableCertificateDownload 是否启用证书下载服务
 * @property certificateDownloadPort 证书下载服务端口
 * @property storageConfig 存储配置
 * @property dataStoragePath 数据存储路径
 * @property logStoragePath 日志存储路径
 * @property maxConnections 最大并发连接数
 * @property connectionTimeout 连接超时时间（毫秒）
 * @property readTimeout 读取超时时间（毫秒）
 * @property writeTimeout 写入超时时间（毫秒）
 * @property bufferSize 缓冲区大小（字节）
 * @property enableLogging 是否启用日志记录
 * @property logLevel 日志级别
 * @property enableMetrics 是否启用性能指标收集
 * @property enableDataMasking 是否启用数据脱敏
 * @property customAttributes 自定义属性
 */
@Serializable
data class SDKConfig(
    val port: Int = 8888,
    val protocols: Set<Protocol> = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET, Protocol.MQTT),
    val enableSslMitm: Boolean = true,
    val sslFallbackStrategy: FallbackStrategyType = FallbackStrategyType.TUNNEL,

    // SSL证书配置
    val sslCertificatePath: String = "./ssl/certificates",
    val sslKeyStorePath: String = "./ssl/keystore.p12",
    val sslKeyStorePassword: String = "packetcapture",
    val enableCertificateDownload: Boolean = true,
    val certificateDownloadPort: Int = 8889,

    // 存储配置
    val storageConfig: StorageConfig = StorageConfig.MEMORY,
    val dataStoragePath: String = "./data",
    val logStoragePath: String = "./logs",

    // 网络配置
    val maxConnections: Int = 10000,
    val connectionTimeout: Long = 30000, // 30秒
    val readTimeout: Long = 60000, // 60秒
    val writeTimeout: Long = 60000, // 60秒
    val bufferSize: Int = 8192, // 8KB

    // 日志配置
    val enableLogging: Boolean = true,
    val logLevel: LogLevel = LogLevel.INFO,

    // 其他配置
    val enableMetrics: Boolean = true,
    val enableDataMasking: Boolean = false,
    val customAttributes: Map<String, String> = emptyMap()
) {
    /**
     * 验证配置的有效性
     */
    fun validate(): List<String> {
        val errors = mutableListOf<String>()

        // 基本配置验证
        if (port < 1 || port > 65535) {
            errors.add("端口号必须在1-65535范围内")
        }

        if (protocols.isEmpty()) {
            errors.add("至少需要支持一种协议")
        }

        if (maxConnections < 1) {
            errors.add("最大连接数必须大于0")
        }

        if (connectionTimeout < 1000) {
            errors.add("连接超时时间不能少于1秒")
        }

        if (bufferSize < 1024) {
            errors.add("缓冲区大小不能少于1KB")
        }

        // SSL配置验证
        if (enableCertificateDownload) {
            if (certificateDownloadPort < 1 || certificateDownloadPort > 65535) {
                errors.add("证书下载端口必须在1-65535范围内")
            }
            if (certificateDownloadPort == port) {
                errors.add("证书下载端口不能与主端口相同")
            }
        }

        // 路径验证
        if (sslCertificatePath.isBlank()) {
            errors.add("SSL证书路径不能为空")
        }

        if (sslKeyStorePath.isBlank()) {
            errors.add("SSL密钥库路径不能为空")
        }

        if (dataStoragePath.isBlank()) {
            errors.add("数据存储路径不能为空")
        }

        if (logStoragePath.isBlank()) {
            errors.add("日志存储路径不能为空")
        }

        return errors
    }
    
    /**
     * 是否为有效配置
     */
    val isValid: Boolean
        get() = validate().isEmpty()
    
    /**
     * 更新配置
     */
    fun update(patch: ConfigPatch): SDKConfig {
        return copy(
            port = patch.port ?: this.port,
            protocols = patch.protocols ?: this.protocols,
            enableSslMitm = patch.enableSslMitm ?: this.enableSslMitm,
            sslFallbackStrategy = patch.sslFallbackStrategy ?: this.sslFallbackStrategy,

            // SSL证书配置
            sslCertificatePath = patch.sslCertificatePath ?: this.sslCertificatePath,
            sslKeyStorePath = patch.sslKeyStorePath ?: this.sslKeyStorePath,
            sslKeyStorePassword = patch.sslKeyStorePassword ?: this.sslKeyStorePassword,
            enableCertificateDownload = patch.enableCertificateDownload ?: this.enableCertificateDownload,
            certificateDownloadPort = patch.certificateDownloadPort ?: this.certificateDownloadPort,

            // 存储配置
            storageConfig = patch.storageConfig ?: this.storageConfig,
            dataStoragePath = patch.dataStoragePath ?: this.dataStoragePath,
            logStoragePath = patch.logStoragePath ?: this.logStoragePath,

            // 网络配置
            maxConnections = patch.maxConnections ?: this.maxConnections,
            connectionTimeout = patch.connectionTimeout ?: this.connectionTimeout,
            readTimeout = patch.readTimeout ?: this.readTimeout,
            writeTimeout = patch.writeTimeout ?: this.writeTimeout,
            bufferSize = patch.bufferSize ?: this.bufferSize,

            // 日志配置
            enableLogging = patch.enableLogging ?: this.enableLogging,
            logLevel = patch.logLevel ?: this.logLevel,

            // 其他配置
            enableMetrics = patch.enableMetrics ?: this.enableMetrics,
            enableDataMasking = patch.enableDataMasking ?: this.enableDataMasking,
            customAttributes = patch.customAttributes ?: this.customAttributes
        )
    }
}

/**
 * 配置更新补丁
 */
@Serializable
data class ConfigPatch(
    val port: Int? = null,
    val protocols: Set<Protocol>? = null,
    val enableSslMitm: Boolean? = null,
    val sslFallbackStrategy: FallbackStrategyType? = null,

    // SSL证书配置
    val sslCertificatePath: String? = null,
    val sslKeyStorePath: String? = null,
    val sslKeyStorePassword: String? = null,
    val enableCertificateDownload: Boolean? = null,
    val certificateDownloadPort: Int? = null,

    // 存储配置
    val storageConfig: StorageConfig? = null,
    val dataStoragePath: String? = null,
    val logStoragePath: String? = null,

    // 网络配置
    val maxConnections: Int? = null,
    val connectionTimeout: Long? = null,
    val readTimeout: Long? = null,
    val writeTimeout: Long? = null,
    val bufferSize: Int? = null,

    // 日志配置
    val enableLogging: Boolean? = null,
    val logLevel: LogLevel? = null,

    // 其他配置
    val enableMetrics: Boolean? = null,
    val enableDataMasking: Boolean? = null,
    val customAttributes: Map<String, String>? = null
)

/**
 * 降级策略类型
 */
@Serializable
enum class FallbackStrategyType {
    /** 隧道模式 - 直接转发加密流量 */
    TUNNEL,
    /** 拒绝连接 */
    REJECT,
    /** 记录并继续 */
    LOG_AND_CONTINUE
}

/**
 * 存储配置
 */
@Serializable
enum class StorageConfig {
    /** 内存存储 */
    MEMORY,
    /** 文件存储 */
    FILE,
    /** 数据库存储 */
    DATABASE,
    /** 云存储 */
    CLOUD
}

/**
 * 日志级别
 */
@Serializable
enum class LogLevel {
    TRACE,
    DEBUG,
    INFO,
    WARN,
    ERROR
}
