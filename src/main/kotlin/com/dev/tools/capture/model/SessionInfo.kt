package com.dev.tools.capture.model

import kotlinx.serialization.Serializable
import java.time.Instant
import java.util.*

/**
 * 会话信息
 * 
 * @property id 会话唯一标识符
 * @property source 来源地址（客户端地址）
 * @property destination 目标地址（服务器地址）
 * @property protocol 使用的协议类型
 * @property startTime 会话开始时间
 * @property endTime 会话结束时间（可选）
 * @property attributes 自定义属性映射
 */
@Serializable
data class SessionInfo(
    val id: String = UUID.randomUUID().toString(),
    val source: String,
    val destination: String,
    val protocol: Protocol,
    @Serializable(with = InstantSerializer::class)
    val startTime: Instant = Instant.now(),
    @Serializable(with = InstantSerializer::class)
    val endTime: Instant? = null,
    val attributes: Map<String, String> = emptyMap()
) {
    /**
     * 会话是否已结束
     */
    val isEnded: Boolean
        get() = endTime != null
    
    /**
     * 会话持续时间（毫秒）
     */
    val duration: Long?
        get() = endTime?.let { it.toEpochMilli() - startTime.toEpochMilli() }
    
    /**
     * 添加自定义属性
     */
    fun withAttribute(key: String, value: String): SessionInfo {
        return copy(attributes = attributes + (key to value))
    }
    
    /**
     * 添加多个自定义属性
     */
    fun withAttributes(newAttributes: Map<String, String>): SessionInfo {
        return copy(attributes = attributes + newAttributes)
    }
    
    /**
     * 结束会话
     */
    fun end(endTime: Instant = Instant.now()): SessionInfo {
        return copy(endTime = endTime)
    }
    
    companion object {
        /**
         * 创建新的会话信息
         */
        fun create(
            source: String,
            destination: String,
            protocol: Protocol,
            attributes: Map<String, String> = emptyMap()
        ): SessionInfo {
            return SessionInfo(
                source = source,
                destination = destination,
                protocol = protocol,
                attributes = attributes
            )
        }
    }
}
