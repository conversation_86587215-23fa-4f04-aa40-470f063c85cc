package com.dev.tools.capture.model

import kotlinx.serialization.Serializable
import java.time.Instant
import java.util.*

/**
 * Mock规则
 * 
 * @property id 规则唯一标识符
 * @property name 规则名称
 * @property description 规则描述
 * @property enabled 是否启用
 * @property priority 优先级（数值越大优先级越高）
 * @property condition 匹配条件函数
 * @property response 响应生成函数
 * @property createdAt 创建时间
 * @property updatedAt 更新时间
 * @property metadata 规则元数据
 */
data class MockRule(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val description: String = "",
    val enabled: Boolean = true,
    val priority: Int = 0,
    val condition: (PacketData, ProtocolContext) -> Boolean,
    val response: (PacketData, ProtocolContext) -> PacketData,
    val createdAt: Instant = Instant.now(),
    val updatedAt: Instant = Instant.now(),
    val metadata: Map<String, String> = emptyMap()
) {
    /**
     * 检查规则是否匹配给定的数据包和上下文
     */
    fun matches(packet: PacketData, context: ProtocolContext): Boolean {
        return enabled && condition(packet, context)
    }
    
    /**
     * 生成Mock响应
     */
    fun generateResponse(packet: PacketData, context: ProtocolContext): PacketData {
        return response(packet, context)
    }
    
    /**
     * 更新规则
     */
    fun update(
        name: String? = null,
        description: String? = null,
        enabled: Boolean? = null,
        priority: Int? = null,
        condition: ((PacketData, ProtocolContext) -> Boolean)? = null,
        response: ((PacketData, ProtocolContext) -> PacketData)? = null,
        metadata: Map<String, String>? = null
    ): MockRule {
        return copy(
            name = name ?: this.name,
            description = description ?: this.description,
            enabled = enabled ?: this.enabled,
            priority = priority ?: this.priority,
            condition = condition ?: this.condition,
            response = response ?: this.response,
            updatedAt = Instant.now(),
            metadata = metadata ?: this.metadata
        )
    }
    
    /**
     * 添加元数据
     */
    fun withMetadata(key: String, value: String): MockRule {
        return copy(
            metadata = metadata + (key to value),
            updatedAt = Instant.now()
        )
    }
    
    companion object {
        /**
         * 创建简单的URL匹配规则
         */
        fun urlMatches(
            name: String,
            urlPattern: String,
            responseData: String,
            statusCode: Int = 200,
            headers: Map<String, String> = emptyMap()
        ): MockRule {
            return MockRule(
                name = name,
                description = "URL匹配规则: $urlPattern",
                condition = { packet, context ->
                    context.protocol in listOf(Protocol.HTTP, Protocol.HTTPS) &&
                    packet.rawDataAsString.contains(urlPattern)
                },
                response = { _, _ ->
                    val responseHeaders = headers.entries.joinToString("\r\n") { "${it.key}: ${it.value}" }
                    val httpResponse = "HTTP/1.1 $statusCode OK\r\n" +
                            "Content-Type: application/json\r\n" +
                            "Content-Length: ${responseData.length}\r\n" +
                            responseHeaders +
                            (if (responseHeaders.isNotEmpty()) "\r\n" else "") +
                            "\r\n" +
                            responseData
                    
                    PacketData.fromString(
                        httpResponse,
                        PacketData.PacketDirection.SERVER_TO_CLIENT,
                        mapOf("mock" to "true", "rule" to name)
                    )
                }
            )
        }
        
        /**
         * 创建协议匹配规则
         */
        fun protocolMatches(
            name: String,
            protocol: Protocol,
            responseData: ByteArray,
            metadata: Map<String, String> = emptyMap()
        ): MockRule {
            return MockRule(
                name = name,
                description = "协议匹配规则: $protocol",
                condition = { _, context ->
                    context.protocol == protocol
                },
                response = { _, _ ->
                    PacketData(
                        rawData = responseData,
                        direction = PacketData.PacketDirection.SERVER_TO_CLIENT,
                        metadata = metadata + mapOf("mock" to "true", "rule" to name)
                    )
                }
            )
        }
        
        /**
         * 创建内容匹配规则
         */
        fun contentContains(
            name: String,
            searchText: String,
            responseData: String,
            ignoreCase: Boolean = true
        ): MockRule {
            return MockRule(
                name = name,
                description = "内容匹配规则: $searchText",
                condition = { packet, _ ->
                    packet.rawDataAsString.contains(searchText, ignoreCase)
                },
                response = { _, _ ->
                    PacketData.fromString(
                        responseData,
                        PacketData.PacketDirection.SERVER_TO_CLIENT,
                        mapOf("mock" to "true", "rule" to name)
                    )
                }
            )
        }
    }
}
