package com.dev.tools.capture.model

/**
 * 支持的网络协议类型
 */
enum class Protocol {
    /**
     * HTTP协议
     */
    HTTP,
    
    /**
     * HTTPS协议 (HTTP over SSL/TLS)
     */
    HTTPS,
    
    /**
     * WebSocket协议
     */
    WEBSOCKET,
    
    /**
     * WebSocket Secure协议 (WebSocket over SSL/TLS)
     */
    WSS,
    
    /**
     * MQTT协议
     */
    MQTT,
    
    /**
     * MQTT Secure协议 (MQTT over SSL/TLS)
     */
    MQTTS;
    
    /**
     * 判断协议是否使用SSL/TLS加密
     */
    val isSecure: Boolean
        get() = when (this) {
            HTTPS, WSS, MQTTS -> true
            HTTP, WEBSOCKET, MQTT -> false
        }
    
    /**
     * 获取协议的默认端口
     */
    val defaultPort: Int
        get() = when (this) {
            HTTP -> 80
            HTTPS -> 443
            WEBSOCKET -> 80
            WSS -> 443
            MQTT -> 1883
            MQTTS -> 8883
        }
    
    /**
     * 获取协议的基础类型（不考虑SSL）
     */
    val baseProtocol: Protocol
        get() = when (this) {
            HTTPS -> HTTP
            WSS -> WEBSOCKET
            MQTTS -> MQTT
            else -> this
        }
}
