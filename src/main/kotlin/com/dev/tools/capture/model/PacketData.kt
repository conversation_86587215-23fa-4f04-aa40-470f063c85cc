package com.dev.tools.capture.model

import kotlinx.serialization.Serializable
import java.time.Instant
import java.util.*

/**
 * 网络数据包
 * 
 * @property id 数据包唯一标识符
 * @property rawData 原始字节数据
 * @property parsedData 解析后的协议数据（可选）
 * @property metadata 元数据映射
 * @property timestamp 数据包时间戳
 * @property direction 数据包方向
 * @property size 数据包大小（字节）
 */
@Serializable
data class PacketData(
    val id: String = UUID.randomUUID().toString(),
    val rawData: ByteArray,
    val parsedData: String? = null, // 使用String而不是Any以支持序列化
    val metadata: Map<String, String> = emptyMap(),
    @Serializable(with = InstantSerializer::class)
    val timestamp: Instant = Instant.now(),
    val direction: PacketDirection = PacketDirection.UNKNOWN,
    val size: Int = rawData.size
) {
    /**
     * 数据包方向枚举
     */
    @Serializable
    enum class PacketDirection {
        /** 客户端到服务器 */
        CLIENT_TO_SERVER,
        /** 服务器到客户端 */
        SERVER_TO_CLIENT,
        /** 未知方向 */
        UNKNOWN
    }
    
    /**
     * 是否为空数据包
     */
    val isEmpty: Boolean
        get() = rawData.isEmpty()
    
    /**
     * 获取原始数据的字符串表示（UTF-8）
     */
    val rawDataAsString: String
        get() = try {
            String(rawData, Charsets.UTF_8)
        } catch (e: Exception) {
            "Binary data (${rawData.size} bytes)"
        }
    
    /**
     * 添加元数据
     */
    fun withMetadata(key: String, value: String): PacketData {
        return copy(metadata = metadata + (key to value))
    }
    
    /**
     * 添加多个元数据
     */
    fun withMetadata(newMetadata: Map<String, String>): PacketData {
        return copy(metadata = metadata + newMetadata)
    }
    
    /**
     * 设置解析后的数据
     */
    fun withParsedData(data: String): PacketData {
        return copy(parsedData = data)
    }
    
    /**
     * 设置数据包方向
     */
    fun withDirection(direction: PacketDirection): PacketData {
        return copy(direction = direction)
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as PacketData
        
        if (id != other.id) return false
        if (!rawData.contentEquals(other.rawData)) return false
        if (parsedData != other.parsedData) return false
        if (metadata != other.metadata) return false
        if (timestamp != other.timestamp) return false
        if (direction != other.direction) return false
        if (size != other.size) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + rawData.contentHashCode()
        result = 31 * result + (parsedData?.hashCode() ?: 0)
        result = 31 * result + metadata.hashCode()
        result = 31 * result + timestamp.hashCode()
        result = 31 * result + direction.hashCode()
        result = 31 * result + size
        return result
    }
    
    companion object {
        /**
         * 创建客户端到服务器的数据包
         */
        fun clientToServer(
            data: ByteArray,
            metadata: Map<String, String> = emptyMap()
        ): PacketData {
            return PacketData(
                rawData = data,
                direction = PacketDirection.CLIENT_TO_SERVER,
                metadata = metadata
            )
        }
        
        /**
         * 创建服务器到客户端的数据包
         */
        fun serverToClient(
            data: ByteArray,
            metadata: Map<String, String> = emptyMap()
        ): PacketData {
            return PacketData(
                rawData = data,
                direction = PacketDirection.SERVER_TO_CLIENT,
                metadata = metadata
            )
        }
        
        /**
         * 从字符串创建数据包
         */
        fun fromString(
            data: String,
            direction: PacketDirection = PacketDirection.UNKNOWN,
            metadata: Map<String, String> = emptyMap()
        ): PacketData {
            return PacketData(
                rawData = data.toByteArray(Charsets.UTF_8),
                direction = direction,
                metadata = metadata
            )
        }
    }
}
