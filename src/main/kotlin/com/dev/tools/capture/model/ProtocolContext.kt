package com.dev.tools.capture.model

import kotlinx.serialization.Serializable

/**
 * 协议上下文信息
 * 
 * @property protocol 协议类型
 * @property session 会话信息
 * @property sslEnabled 是否启用SSL/TLS
 * @property sslHandshakeCompleted SSL握手是否完成
 * @property clientCertificate 客户端证书信息（可选）
 * @property serverCertificate 服务器证书信息（可选）
 * @property metadata 协议特定的元数据
 */
@Serializable
data class ProtocolContext(
    val protocol: Protocol,
    val session: SessionInfo,
    val sslEnabled: Boolean = protocol.isSecure,
    val sslHandshakeCompleted: Boolean = false,
    val clientCertificate: String? = null,
    val serverCertificate: String? = null,
    val metadata: Map<String, String> = emptyMap()
) {
    /**
     * 是否为安全连接
     */
    val isSecure: Boolean
        get() = sslEnabled && sslHandshakeCompleted
    
    /**
     * 添加元数据
     */
    fun withMetadata(key: String, value: String): ProtocolContext {
        return copy(metadata = metadata + (key to value))
    }
    
    /**
     * 添加多个元数据
     */
    fun withMetadata(newMetadata: Map<String, String>): ProtocolContext {
        return copy(metadata = metadata + newMetadata)
    }
    
    /**
     * 更新SSL状态
     */
    fun withSslCompleted(
        clientCert: String? = null,
        serverCert: String? = null
    ): ProtocolContext {
        return copy(
            sslHandshakeCompleted = true,
            clientCertificate = clientCert,
            serverCertificate = serverCert
        )
    }
    
    /**
     * 更新会话信息
     */
    fun withSession(newSession: SessionInfo): ProtocolContext {
        return copy(session = newSession)
    }
    
    companion object {
        /**
         * 创建新的协议上下文
         */
        fun create(
            protocol: Protocol,
            source: String,
            destination: String,
            sessionAttributes: Map<String, String> = emptyMap(),
            contextMetadata: Map<String, String> = emptyMap()
        ): ProtocolContext {
            val session = SessionInfo.create(
                source = source,
                destination = destination,
                protocol = protocol,
                attributes = sessionAttributes
            )
            
            return ProtocolContext(
                protocol = protocol,
                session = session,
                metadata = contextMetadata
            )
        }
    }
}
