package com.dev.tools.capture.callback

import com.dev.tools.capture.api.PacketCallback
import com.dev.tools.capture.exception.CallbackException
import com.dev.tools.capture.exception.CaptureException
import com.dev.tools.capture.model.PacketData
import com.dev.tools.capture.model.ProtocolContext
import kotlinx.coroutines.*
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 回调管理器
 * 
 * 负责管理所有注册的回调，并提供线程安全的回调执行机制
 */
class CallbackManager {
    
    private val logger = LoggerFactory.getLogger(CallbackManager::class.java)
    
    // 回调存储：ID -> 回调实例
    private val callbacks = ConcurrentHashMap<String, PacketCallback>()
    
    // 回调反向映射：回调实例 -> ID
    private val callbackToId = ConcurrentHashMap<PacketCallback, String>()
    
    // 失败的回调列表（避免重复调用失败的回调）
    private val failedCallbacks = CopyOnWriteArrayList<String>()
    
    // 回调执行的协程作用域
    private val callbackScope = CoroutineScope(
        SupervisorJob() + 
        Dispatchers.Default + 
        CoroutineName("CallbackManager")
    )
    
    /**
     * 注册回调
     * 
     * @param callback 回调实例
     * @return 回调ID
     */
    fun register(callback: PacketCallback): String {
        val callbackId = UUID.randomUUID().toString()
        
        callbacks[callbackId] = callback
        callbackToId[callback] = callbackId
        
        // 从失败列表中移除（如果存在）
        failedCallbacks.remove(callbackId)
        
        logger.debug("注册回调: $callbackId, 总数: ${callbacks.size}")
        return callbackId
    }
    
    /**
     * 注销回调（通过实例）
     * 
     * @param callback 回调实例
     * @return 是否成功注销
     */
    fun unregister(callback: PacketCallback): Boolean {
        val callbackId = callbackToId.remove(callback)
        return if (callbackId != null) {
            callbacks.remove(callbackId)
            failedCallbacks.remove(callbackId)
            logger.debug("注销回调: $callbackId, 剩余: ${callbacks.size}")
            true
        } else {
            false
        }
    }
    
    /**
     * 注销回调（通过ID）
     * 
     * @param callbackId 回调ID
     * @return 是否成功注销
     */
    fun unregister(callbackId: String): Boolean {
        val callback = callbacks.remove(callbackId)
        return if (callback != null) {
            callbackToId.remove(callback)
            failedCallbacks.remove(callbackId)
            logger.debug("注销回调ID: $callbackId, 剩余: ${callbacks.size}")
            true
        } else {
            false
        }
    }
    
    /**
     * 获取回调数量
     */
    fun getCallbackCount(): Int = callbacks.size
    
    /**
     * 获取活跃回调数量（排除失败的回调）
     */
    fun getActiveCallbackCount(): Int = callbacks.size - failedCallbacks.size
    
    /**
     * 通知数据包接收
     * 
     * @param packet 数据包
     * @param context 协议上下文
     */
    fun notifyPacketReceived(packet: PacketData, context: ProtocolContext) {
        if (callbacks.isEmpty()) return
        
        callbackScope.launch {
            callbacks.forEach { (callbackId, callback) ->
                if (callbackId !in failedCallbacks) {
                    launch {
                        try {
                            callback.onPacketReceived(packet, context)
                        } catch (e: Exception) {
                            handleCallbackError(callbackId, "onPacketReceived", e, context)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 通知错误
     * 
     * @param exception 异常
     * @param context 协议上下文（可选）
     */
    fun notifyError(exception: CaptureException, context: ProtocolContext?) {
        if (callbacks.isEmpty()) return
        
        callbackScope.launch {
            callbacks.forEach { (callbackId, callback) ->
                if (callbackId !in failedCallbacks) {
                    launch {
                        try {
                            callback.onError(exception, context)
                        } catch (e: Exception) {
                            handleCallbackError(callbackId, "onError", e, context)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 通知会话开始
     * 
     * @param context 协议上下文
     */
    fun notifySessionStarted(context: ProtocolContext) {
        if (callbacks.isEmpty()) return
        
        callbackScope.launch {
            callbacks.forEach { (callbackId, callback) ->
                if (callbackId !in failedCallbacks) {
                    launch {
                        try {
                            callback.onSessionStarted(context)
                        } catch (e: Exception) {
                            handleCallbackError(callbackId, "onSessionStarted", e, context)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 通知会话结束
     * 
     * @param context 协议上下文
     */
    fun notifySessionEnded(context: ProtocolContext) {
        if (callbacks.isEmpty()) return
        
        callbackScope.launch {
            callbacks.forEach { (callbackId, callback) ->
                if (callbackId !in failedCallbacks) {
                    launch {
                        try {
                            callback.onSessionEnded(context)
                        } catch (e: Exception) {
                            handleCallbackError(callbackId, "onSessionEnded", e, context)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 通知SSL握手完成
     * 
     * @param context 协议上下文
     */
    fun notifySslHandshakeCompleted(context: ProtocolContext) {
        if (callbacks.isEmpty()) return
        
        callbackScope.launch {
            callbacks.forEach { (callbackId, callback) ->
                if (callbackId !in failedCallbacks) {
                    launch {
                        try {
                            callback.onSslHandshakeCompleted(context)
                        } catch (e: Exception) {
                            handleCallbackError(callbackId, "onSslHandshakeCompleted", e, context)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 通知Mock规则匹配
     * 
     * @param packet 原始数据包
     * @param mockResponse Mock响应
     * @param context 协议上下文
     * @param ruleId 规则ID
     */
    fun notifyMockRuleMatched(
        packet: PacketData,
        mockResponse: PacketData,
        context: ProtocolContext,
        ruleId: String
    ) {
        if (callbacks.isEmpty()) return
        
        callbackScope.launch {
            callbacks.forEach { (callbackId, callback) ->
                if (callbackId !in failedCallbacks) {
                    launch {
                        try {
                            callback.onMockRuleMatched(packet, mockResponse, context, ruleId)
                        } catch (e: Exception) {
                            handleCallbackError(callbackId, "onMockRuleMatched", e, context)
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 处理回调执行错误
     */
    private fun handleCallbackError(
        callbackId: String,
        methodName: String,
        error: Exception,
        context: ProtocolContext?
    ) {
        logger.warn("回调执行失败 [$callbackId.$methodName]: ${error.message}")
        
        // 将失败的回调添加到失败列表，避免后续继续调用
        if (!failedCallbacks.contains(callbackId)) {
            failedCallbacks.add(callbackId)
            logger.warn("回调 $callbackId 已被标记为失败，将不再接收后续通知")
        }
        
        // 创建回调异常并通知其他回调
        val callbackException = CallbackException.executionFailed(
            callbackName = callbackId,
            reason = error.message ?: "未知错误",
            context = context,
            cause = error
        )
        
        // 通知其他正常的回调（避免无限递归）
        callbackScope.launch {
            callbacks.forEach { (otherCallbackId, otherCallback) ->
                if (otherCallbackId != callbackId && otherCallbackId !in failedCallbacks) {
                    try {
                        otherCallback.onError(callbackException, context)
                    } catch (e: Exception) {
                        // 如果通知其他回调也失败，只记录日志，不再递归处理
                        logger.error("通知回调错误时发生异常 [$otherCallbackId]: ${e.message}")
                    }
                }
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        callbackScope.cancel()
        callbacks.clear()
        callbackToId.clear()
        failedCallbacks.clear()
        logger.info("回调管理器已清理")
    }
}
