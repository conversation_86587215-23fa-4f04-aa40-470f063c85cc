package com.dev.tools.capture.exception

import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext

/**
 * 协议解析异常
 */
class ProtocolParseException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.PROTOCOL_PARSE_FAILED,
    context = context,
    details = details
) {
    companion object {
        fun invalidFormat(
            protocol: Protocol,
            reason: String,
            context: ProtocolContext? = null,
            rawData: ByteArray? = null
        ): ProtocolParseException {
            val details = mutableMapOf<String, Any>(
                "protocol" to protocol.name,
                "reason" to reason
            )
            rawData?.let {
                details["dataSize"] = it.size
                details["dataPreview"] = String(it.take(100).toByteArray(), Charsets.UTF_8)
            }
            
            return ProtocolParseException(
                message = "协议解析失败 [$protocol]: $reason",
                context = context,
                details = details
            )
        }
        
        fun unsupportedVersion(
            protocol: Protocol,
            version: String,
            supportedVersions: List<String>,
            context: ProtocolContext? = null
        ): ProtocolParseException {
            return ProtocolParseException(
                message = "不支持的协议版本 [$protocol]: $version",
                context = context,
                details = mapOf(
                    "protocol" to protocol.name,
                    "version" to version,
                    "supportedVersions" to supportedVersions
                )
            )
        }
        
        fun malformedHeader(
            protocol: Protocol,
            headerName: String,
            headerValue: String,
            context: ProtocolContext? = null
        ): ProtocolParseException {
            return ProtocolParseException(
                message = "协议头格式错误 [$protocol]: $headerName = $headerValue",
                context = context,
                details = mapOf(
                    "protocol" to protocol.name,
                    "headerName" to headerName,
                    "headerValue" to headerValue
                )
            )
        }
        
        fun incompleteData(
            protocol: Protocol,
            expectedSize: Int,
            actualSize: Int,
            context: ProtocolContext? = null
        ): ProtocolParseException {
            return ProtocolParseException(
                message = "数据不完整 [$protocol]: 期望${expectedSize}字节, 实际${actualSize}字节",
                context = context,
                details = mapOf(
                    "protocol" to protocol.name,
                    "expectedSize" to expectedSize,
                    "actualSize" to actualSize
                )
            )
        }
    }
}

/**
 * 不支持的协议异常
 */
class UnsupportedProtocolException(
    protocol: String,
    supportedProtocols: Set<Protocol>,
    context: ProtocolContext? = null
) : CaptureException(
    message = "不支持的协议: $protocol",
    errorCode = ErrorCodes.PROTOCOL_UNSUPPORTED,
    context = context,
    details = mapOf(
        "protocol" to protocol,
        "supportedProtocols" to supportedProtocols.map { it.name }
    )
)

/**
 * 协议版本不匹配异常
 */
class ProtocolVersionMismatchException(
    protocol: Protocol,
    clientVersion: String,
    serverVersion: String,
    context: ProtocolContext? = null
) : CaptureException(
    message = "协议版本不匹配 [$protocol]: 客户端=$clientVersion, 服务器=$serverVersion",
    errorCode = ErrorCodes.PROTOCOL_VERSION_MISMATCH,
    context = context,
    details = mapOf(
        "protocol" to protocol.name,
        "clientVersion" to clientVersion,
        "serverVersion" to serverVersion
    )
)
