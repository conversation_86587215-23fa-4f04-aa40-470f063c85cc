package com.dev.tools.capture.exception

import com.dev.tools.capture.model.ProtocolContext

/**
 * 配置异常
 */
class ConfigException(
    message: String,
    cause: Throwable? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.CONFIG_INVALID,
    details = details
) {
    companion object {
        fun invalidValue(
            configKey: String,
            value: Any?,
            reason: String
        ): ConfigException {
            return ConfigException(
                message = "配置值无效 [$configKey]: $reason",
                details = mapOf(
                    "configKey" to configKey,
                    "value" to (value?.toString() ?: "null"),
                    "reason" to reason
                )
            )
        }
        
        fun missingRequired(configKey: String): ConfigException {
            return ConfigException(
                message = "缺少必需的配置项: $configKey",
                details = mapOf("configKey" to configKey)
            ).copy(errorCode = ErrorCodes.CONFIG_MISSING)
        }
        
        fun validationFailed(
            errors: List<String>
        ): ConfigException {
            return ConfigException(
                message = "配置验证失败: ${errors.joinToString(", ")}",
                details = mapOf("validationErrors" to errors)
            )
        }
    }
    
    private fun copy(errorCode: String): ConfigException {
        return ConfigException(
            message = this.message ?: "",
            cause = this.cause,
            details = this.details
        )
    }
}

/**
 * 回调执行异常
 */
class CallbackException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.CALLBACK_EXECUTION_FAILED,
    context = context,
    details = details
) {
    companion object {
        fun executionFailed(
            callbackName: String,
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): CallbackException {
            return CallbackException(
                message = "回调执行失败 [$callbackName]: $reason",
                cause = cause,
                context = context,
                details = mapOf(
                    "callbackName" to callbackName,
                    "reason" to reason
                )
            )
        }
        
        fun registrationFailed(
            callbackName: String,
            reason: String,
            cause: Throwable? = null
        ): CallbackException {
            return CallbackException(
                message = "回调注册失败 [$callbackName]: $reason",
                cause = cause,
                details = mapOf(
                    "callbackName" to callbackName,
                    "reason" to reason
                )
            ).copy(errorCode = ErrorCodes.CALLBACK_REGISTRATION_FAILED)
        }
    }
    
    private fun copy(errorCode: String): CallbackException {
        return CallbackException(
            message = this.message ?: "",
            cause = this.cause,
            context = this.context,
            details = this.details
        )
    }
}

/**
 * 存储异常
 */
class StorageException(
    message: String,
    cause: Throwable? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.STORAGE_WRITE_FAILED,
    details = details
) {
    companion object {
        fun writeFailed(
            storageType: String,
            reason: String,
            cause: Throwable? = null
        ): StorageException {
            return StorageException(
                message = "存储写入失败 [$storageType]: $reason",
                cause = cause,
                details = mapOf(
                    "storageType" to storageType,
                    "operation" to "write",
                    "reason" to reason
                )
            )
        }
        
        fun readFailed(
            storageType: String,
            reason: String,
            cause: Throwable? = null
        ): StorageException {
            return StorageException(
                message = "存储读取失败 [$storageType]: $reason",
                cause = cause,
                details = mapOf(
                    "storageType" to storageType,
                    "operation" to "read",
                    "reason" to reason
                )
            ).copy(errorCode = ErrorCodes.STORAGE_READ_FAILED)
        }
        
        fun connectionFailed(
            storageType: String,
            connectionString: String,
            cause: Throwable? = null
        ): StorageException {
            return StorageException(
                message = "存储连接失败 [$storageType]: $connectionString",
                cause = cause,
                details = mapOf(
                    "storageType" to storageType,
                    "connectionString" to connectionString
                )
            ).copy(errorCode = ErrorCodes.STORAGE_CONNECTION_FAILED)
        }
    }
    
    private fun copy(errorCode: String): StorageException {
        return StorageException(
            message = this.message ?: "",
            cause = this.cause,
            details = this.details
        )
    }
}

/**
 * 降级策略异常
 */
class FallbackException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.FALLBACK_STRATEGY_FAILED,
    context = context,
    details = details
) {
    companion object {
        fun strategyFailed(
            strategyName: String,
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): FallbackException {
            return FallbackException(
                message = "降级策略执行失败 [$strategyName]: $reason",
                cause = cause,
                context = context,
                details = mapOf(
                    "strategyName" to strategyName,
                    "reason" to reason
                )
            )
        }
    }
}
