package com.dev.tools.capture.exception

import com.dev.tools.capture.model.Protocol
import com.dev.tools.capture.model.ProtocolContext
import java.time.Instant

/**
 * 抓包异常基类
 * 
 * @property errorCode 错误代码
 * @property context 协议上下文（可选）
 * @property timestamp 异常发生时间
 * @property details 详细错误信息
 */
sealed class CaptureException(
    message: String,
    cause: Throwable? = null,
    val errorCode: String,
    val context: ProtocolContext? = null,
    val timestamp: Instant = Instant.now(),
    val details: Map<String, Any> = emptyMap()
) : Exception(message, cause) {
    
    /**
     * 获取异常的完整描述
     */
    fun getFullDescription(): String {
        val sb = StringBuilder()
        sb.append("错误代码: $errorCode\n")
        sb.append("错误消息: $message\n")
        sb.append("发生时间: $timestamp\n")
        
        context?.let {
            sb.append("协议: ${it.protocol}\n")
            sb.append("会话ID: ${it.session.id}\n")
            sb.append("来源: ${it.session.source}\n")
            sb.append("目标: ${it.session.destination}\n")
        }
        
        if (details.isNotEmpty()) {
            sb.append("详细信息:\n")
            details.forEach { (key, value) ->
                sb.append("  $key: $value\n")
            }
        }
        
        cause?.let {
            sb.append("原因: ${it.message}\n")
        }
        
        return sb.toString()
    }
    
    /**
     * 转换为映射格式
     */
    fun toMap(): Map<String, Any> {
        val map = mutableMapOf<String, Any>(
            "errorCode" to errorCode,
            "message" to (message ?: ""),
            "timestamp" to timestamp.toEpochMilli(),
            "type" to this::class.simpleName!!
        )
        
        context?.let {
            map["context"] = mapOf(
                "protocol" to it.protocol.name,
                "sessionId" to it.session.id,
                "source" to it.session.source,
                "destination" to it.session.destination,
                "sslEnabled" to it.sslEnabled
            )
        }
        
        if (details.isNotEmpty()) {
            map["details"] = details
        }
        
        cause?.let {
            map["cause"] = it.message ?: it::class.simpleName!!
        }
        
        return map
    }
    
    companion object {
        /**
         * 错误代码常量
         */
        object ErrorCodes {
            const val SSL_DECRYPTION_FAILED = "SSL_001"
            const val SSL_HANDSHAKE_FAILED = "SSL_002"
            const val SSL_CERTIFICATE_INVALID = "SSL_003"
            const val SSL_CERTIFICATE_GENERATION_FAILED = "SSL_004"
            
            const val PROTOCOL_PARSE_FAILED = "PROTOCOL_001"
            const val PROTOCOL_UNSUPPORTED = "PROTOCOL_002"
            const val PROTOCOL_VERSION_MISMATCH = "PROTOCOL_003"
            
            const val MOCK_PROCESSING_FAILED = "MOCK_001"
            const val MOCK_RULE_INVALID = "MOCK_002"
            const val MOCK_RESPONSE_GENERATION_FAILED = "MOCK_003"
            
            const val NETWORK_CONNECTION_FAILED = "NETWORK_001"
            const val NETWORK_TIMEOUT = "NETWORK_002"
            const val NETWORK_IO_ERROR = "NETWORK_003"
            
            const val CONFIG_INVALID = "CONFIG_001"
            const val CONFIG_MISSING = "CONFIG_002"
            
            const val CALLBACK_EXECUTION_FAILED = "CALLBACK_001"
            const val CALLBACK_REGISTRATION_FAILED = "CALLBACK_002"
            
            const val STORAGE_WRITE_FAILED = "STORAGE_001"
            const val STORAGE_READ_FAILED = "STORAGE_002"
            const val STORAGE_CONNECTION_FAILED = "STORAGE_003"
            
            const val FALLBACK_STRATEGY_FAILED = "FALLBACK_001"
            
            const val UNKNOWN_ERROR = "UNKNOWN_001"
        }
    }
}
