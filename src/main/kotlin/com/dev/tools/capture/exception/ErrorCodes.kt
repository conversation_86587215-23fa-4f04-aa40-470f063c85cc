package com.dev.tools.capture.exception

/**
 * 错误代码常量
 */
object ErrorCodes {
    const val SSL_DECRYPTION_FAILED = "SSL_001"
    const val SSL_HANDSHAKE_FAILED = "SSL_002"
    const val SSL_CERTIFICATE_INVALID = "SSL_003"
    const val SSL_CERTIFICATE_GENERATION_FAILED = "SSL_004"
    
    const val PROTOCOL_PARSE_FAILED = "PROTOCOL_001"
    const val PROTOCOL_UNSUPPORTED = "PROTOCOL_002"
    const val PROTOCOL_VERSION_MISMATCH = "PROTOCOL_003"
    
    const val MOCK_PROCESSING_FAILED = "MOCK_001"
    const val MOCK_RULE_INVALID = "MOCK_002"
    const val MOCK_RESPONSE_GENERATION_FAILED = "MOCK_003"
    
    const val NETWORK_CONNECTION_FAILED = "NETWORK_001"
    const val NETWORK_TIMEOUT = "NETWORK_002"
    const val NETWORK_IO_ERROR = "NETWORK_003"
    
    const val CONFIG_INVALID = "CONFIG_001"
    const val CONFIG_MISSING = "CONFIG_002"
    
    const val CALLBACK_EXECUTION_FAILED = "CALLBACK_001"
    const val CALLBACK_REGISTRATION_FAILED = "CALLBACK_002"
    
    const val STORAGE_WRITE_FAILED = "STORAGE_001"
    const val STORAGE_READ_FAILED = "STORAGE_002"
    const val STORAGE_CONNECTION_FAILED = "STORAGE_003"
    
    const val FALLBACK_STRATEGY_FAILED = "FALLBACK_001"
    
    const val UNKNOWN_ERROR = "UNKNOWN_001"
}
