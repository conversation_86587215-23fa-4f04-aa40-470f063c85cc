package com.dev.tools.capture.exception

import com.dev.tools.capture.model.ProtocolContext
import java.time.Instant

/**
 * SSL解密异常
 */
class SslDecryptionException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.SSL_DECRYPTION_FAILED,
    context = context,
    details = details
) {
    companion object {
        fun handshakeFailed(
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): SslDecryptionException {
            return SslDecryptionException(
                message = "SSL握手失败: $reason",
                cause = cause,
                context = context,
                details = mapOf("reason" to reason)
            )
        }
        
        fun certificateInvalid(
            certificateInfo: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): SslDecryptionException {
            return SslDecryptionException(
                message = "SSL证书无效: $certificateInfo",
                cause = cause,
                context = context,
                details = mapOf("certificate" to certificateInfo)
            )
        }
        
        fun decryptionFailed(
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): SslDecryptionException {
            return SslDecryptionException(
                message = "SSL解密失败: $reason",
                cause = cause,
                context = context,
                details = mapOf("reason" to reason)
            )
        }
    }
}

/**
 * SSL握手异常
 */
class SslHandshakeException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.SSL_HANDSHAKE_FAILED,
    context = context,
    details = details
) {
    companion object {
        fun timeout(
            timeoutMs: Long,
            context: ProtocolContext? = null
        ): SslHandshakeException {
            return SslHandshakeException(
                message = "SSL握手超时: ${timeoutMs}ms",
                context = context,
                details = mapOf("timeout" to timeoutMs)
            )
        }
        
        fun protocolMismatch(
            clientProtocol: String,
            serverProtocol: String,
            context: ProtocolContext? = null
        ): SslHandshakeException {
            return SslHandshakeException(
                message = "SSL协议版本不匹配: 客户端=$clientProtocol, 服务器=$serverProtocol",
                context = context,
                details = mapOf(
                    "clientProtocol" to clientProtocol,
                    "serverProtocol" to serverProtocol
                )
            )
        }
        
        fun cipherSuiteMismatch(
            supportedCiphers: List<String>,
            context: ProtocolContext? = null
        ): SslHandshakeException {
            return SslHandshakeException(
                message = "SSL加密套件不匹配",
                context = context,
                details = mapOf("supportedCiphers" to supportedCiphers)
            )
        }
    }
}

/**
 * SSL证书异常
 */
class SslCertificateException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.SSL_CERTIFICATE_INVALID,
    context = context,
    details = details
) {
    companion object {
        fun expired(
            expiryDate: Instant,
            context: ProtocolContext? = null
        ): SslCertificateException {
            return SslCertificateException(
                message = "SSL证书已过期: $expiryDate",
                context = context,
                details = mapOf("expiryDate" to expiryDate.toEpochMilli())
            )
        }
        
        fun notYetValid(
            validFromDate: Instant,
            context: ProtocolContext? = null
        ): SslCertificateException {
            return SslCertificateException(
                message = "SSL证书尚未生效: $validFromDate",
                context = context,
                details = mapOf("validFromDate" to validFromDate.toEpochMilli())
            )
        }
        
        fun hostnameMismatch(
            expectedHostname: String,
            actualHostname: String,
            context: ProtocolContext? = null
        ): SslCertificateException {
            return SslCertificateException(
                message = "SSL证书主机名不匹配: 期望=$expectedHostname, 实际=$actualHostname",
                context = context,
                details = mapOf(
                    "expectedHostname" to expectedHostname,
                    "actualHostname" to actualHostname
                )
            )
        }
        
        fun untrusted(
            issuer: String,
            context: ProtocolContext? = null
        ): SslCertificateException {
            return SslCertificateException(
                message = "SSL证书不受信任: 颁发者=$issuer",
                context = context,
                details = mapOf("issuer" to issuer)
            )
        }
        
        fun generationFailed(
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): SslCertificateException {
            return SslCertificateException(
                message = "SSL证书生成失败: $reason",
                cause = cause,
                context = context,
                details = mapOf("reason" to reason)
            ).copy(errorCode = ErrorCodes.SSL_CERTIFICATE_GENERATION_FAILED)
        }
    }
    
    private fun copy(errorCode: String): SslCertificateException {
        return SslCertificateException(
            message = this.message ?: "",
            cause = this.cause,
            context = this.context,
            details = this.details
        )
    }
}
