package com.dev.tools.capture.exception

import com.dev.tools.capture.model.ProtocolContext

/**
 * Mock处理异常
 */
class MockProcessingException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.MOCK_PROCESSING_FAILED,
    context = context,
    details = details
) {
    companion object {
        fun ruleExecutionFailed(
            ruleId: String,
            ruleName: String,
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): MockProcessingException {
            return MockProcessingException(
                message = "Mock规则执行失败 [$ruleName]: $reason",
                cause = cause,
                context = context,
                details = mapOf(
                    "ruleId" to ruleId,
                    "ruleName" to ruleName,
                    "reason" to reason
                )
            )
        }
        
        fun conditionEvaluationFailed(
            ruleId: String,
            ruleName: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): MockProcessingException {
            return MockProcessingException(
                message = "Mock规则条件评估失败 [$ruleName]",
                cause = cause,
                context = context,
                details = mapOf(
                    "ruleId" to ruleId,
                    "ruleName" to ruleName
                )
            )
        }
        
        fun responseGenerationFailed(
            ruleId: String,
            ruleName: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): MockProcessingException {
            return MockProcessingException(
                message = "Mock响应生成失败 [$ruleName]",
                cause = cause,
                context = context,
                details = mapOf(
                    "ruleId" to ruleId,
                    "ruleName" to ruleName
                )
            ).copy(errorCode = ErrorCodes.MOCK_RESPONSE_GENERATION_FAILED)
        }
    }
    
    private fun copy(errorCode: String): MockProcessingException {
        return MockProcessingException(
            message = this.message ?: "",
            cause = this.cause,
            context = this.context,
            details = this.details
        )
    }
}

/**
 * Mock规则无效异常
 */
class MockRuleInvalidException(
    ruleId: String,
    ruleName: String,
    reason: String,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = "Mock规则无效 [$ruleName]: $reason",
    errorCode = ErrorCodes.MOCK_RULE_INVALID,
    details = details + mapOf(
        "ruleId" to ruleId,
        "ruleName" to ruleName,
        "reason" to reason
    )
) {
    companion object {
        fun missingCondition(ruleId: String, ruleName: String): MockRuleInvalidException {
            return MockRuleInvalidException(
                ruleId = ruleId,
                ruleName = ruleName,
                reason = "缺少匹配条件"
            )
        }
        
        fun missingResponse(ruleId: String, ruleName: String): MockRuleInvalidException {
            return MockRuleInvalidException(
                ruleId = ruleId,
                ruleName = ruleName,
                reason = "缺少响应生成器"
            )
        }
        
        fun invalidPriority(
            ruleId: String,
            ruleName: String,
            priority: Int
        ): MockRuleInvalidException {
            return MockRuleInvalidException(
                ruleId = ruleId,
                ruleName = ruleName,
                reason = "无效的优先级: $priority",
                details = mapOf("priority" to priority)
            )
        }
        
        fun duplicateId(ruleId: String, existingRuleName: String): MockRuleInvalidException {
            return MockRuleInvalidException(
                ruleId = ruleId,
                ruleName = "未知",
                reason = "规则ID重复，已存在规则: $existingRuleName",
                details = mapOf("existingRuleName" to existingRuleName)
            )
        }
    }
}
