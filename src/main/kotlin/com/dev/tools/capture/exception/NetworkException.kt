package com.dev.tools.capture.exception

import com.dev.tools.capture.model.ProtocolContext

/**
 * 网络连接异常
 */
class NetworkConnectionException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.NETWORK_CONNECTION_FAILED,
    context = context,
    details = details
) {
    companion object {
        fun connectionRefused(
            host: String,
            port: Int,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): NetworkConnectionException {
            return NetworkConnectionException(
                message = "连接被拒绝: $host:$port",
                cause = cause,
                context = context,
                details = mapOf("host" to host, "port" to port)
            )
        }
        
        fun hostUnreachable(
            host: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): NetworkConnectionException {
            return NetworkConnectionException(
                message = "主机不可达: $host",
                cause = cause,
                context = context,
                details = mapOf("host" to host)
            )
        }
        
        fun dnsResolutionFailed(
            hostname: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): NetworkConnectionException {
            return NetworkConnectionException(
                message = "DNS解析失败: $hostname",
                cause = cause,
                context = context,
                details = mapOf("hostname" to hostname)
            )
        }
    }
}

/**
 * 网络超时异常
 */
class NetworkTimeoutException(
    message: String,
    timeoutMs: Long,
    context: ProtocolContext? = null,
    cause: Throwable? = null
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.NETWORK_TIMEOUT,
    context = context,
    details = mapOf("timeoutMs" to timeoutMs)
) {
    companion object {
        fun connectionTimeout(
            timeoutMs: Long,
            context: ProtocolContext? = null
        ): NetworkTimeoutException {
            return NetworkTimeoutException(
                message = "连接超时: ${timeoutMs}ms",
                timeoutMs = timeoutMs,
                context = context
            )
        }
        
        fun readTimeout(
            timeoutMs: Long,
            context: ProtocolContext? = null
        ): NetworkTimeoutException {
            return NetworkTimeoutException(
                message = "读取超时: ${timeoutMs}ms",
                timeoutMs = timeoutMs,
                context = context
            )
        }
        
        fun writeTimeout(
            timeoutMs: Long,
            context: ProtocolContext? = null
        ): NetworkTimeoutException {
            return NetworkTimeoutException(
                message = "写入超时: ${timeoutMs}ms",
                timeoutMs = timeoutMs,
                context = context
            )
        }
    }
}

/**
 * 网络IO异常
 */
class NetworkIOException(
    message: String,
    cause: Throwable? = null,
    context: ProtocolContext? = null,
    details: Map<String, Any> = emptyMap()
) : CaptureException(
    message = message,
    cause = cause,
    errorCode = ErrorCodes.NETWORK_IO_ERROR,
    context = context,
    details = details
) {
    companion object {
        fun readError(
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): NetworkIOException {
            return NetworkIOException(
                message = "读取错误: $reason",
                cause = cause,
                context = context,
                details = mapOf("operation" to "read", "reason" to reason)
            )
        }
        
        fun writeError(
            reason: String,
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): NetworkIOException {
            return NetworkIOException(
                message = "写入错误: $reason",
                cause = cause,
                context = context,
                details = mapOf("operation" to "write", "reason" to reason)
            )
        }
        
        fun connectionClosed(
            context: ProtocolContext? = null,
            cause: Throwable? = null
        ): NetworkIOException {
            return NetworkIOException(
                message = "连接已关闭",
                cause = cause,
                context = context,
                details = mapOf("operation" to "connection_closed")
            )
        }
    }
}
