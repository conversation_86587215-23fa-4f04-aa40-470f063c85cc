package com.dev.tools.capture.security

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import java.security.cert.X509Certificate
import javax.security.auth.x500.X500Principal

class CertificateProviderTest {
    
    private lateinit var certificateProvider: CertificateProvider
    
    @BeforeEach
    fun setUp() {
        certificateProvider = CertificateProvider()
        certificateProvider.generateRootCertificate()
    }
    
    @Test
    fun `test server certificate generation with correct SAN`() {
        val hostname = "qa.kanzhun-inc.com"
        
        // 生成服务器证书
        val certificate = certificateProvider.generateServerCertificate(hostname)
        
        // 验证证书不为空
        assertNotNull(certificate)
        
        // 验证CN
        val subject = certificate.subjectX500Principal.name
        assertTrue(subject.contains("CN=$hostname"), "证书CN应该包含主机名: $subject")
        
        // 验证SAN
        val sanCollection = certificate.subjectAlternativeNames
        assertNotNull(sanCollection, "证书应该包含SAN扩展")
        
        val dnsNames = sanCollection
            ?.filter { it[0] == 2 } // DNS name type
            ?.map { it[1] as String }
            ?: emptyList()
        
        assertTrue(dnsNames.contains(hostname), "SAN应该包含主机名: $dnsNames")
        
        println("证书主题: $subject")
        println("SAN DNS名称: $dnsNames")
        
        // 验证证书有效性
        assertNotNull(certificate.notBefore)
        assertNotNull(certificate.notAfter)
        assertTrue(certificate.notAfter.after(certificate.notBefore))
    }
    
    @Test
    fun `test certificate with wildcard domain`() {
        val hostname = "api.example.com"
        
        val certificate = certificateProvider.generateServerCertificate(hostname)
        
        val sanCollection = certificate.subjectAlternativeNames
        val dnsNames = sanCollection
            ?.filter { it[0] == 2 }
            ?.map { it[1] as String }
            ?: emptyList()
        
        assertTrue(dnsNames.contains(hostname), "SAN应该包含原始主机名")
        assertTrue(dnsNames.contains("*.example.com"), "SAN应该包含通配符域名")
        
        println("通配符测试 - DNS名称: $dnsNames")
    }
}
