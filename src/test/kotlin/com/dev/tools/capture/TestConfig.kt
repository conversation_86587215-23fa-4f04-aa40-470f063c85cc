package com.dev.tools.capture

import com.dev.tools.capture.model.*

/**
 * 测试配置和工具类
 */
object TestConfig {
    
    /**
     * 创建测试用的SDK配置
     */
    fun createTestConfig(): SDKConfig {
        return SDKConfig(
            port = 8888,
            protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET),
            enableSslMitm = false, // 测试时禁用SSL以简化
            sslFallbackStrategy = FallbackStrategyType.LOG_AND_CONTINUE,
            storageConfig = StorageConfig.MEMORY,
            maxConnections = 10,
            connectionTimeout = 5000,
            readTimeout = 5000,
            writeTimeout = 5000,
            bufferSize = 1024,
            enableLogging = false, // 测试时禁用日志
            logLevel = LogLevel.ERROR,
            enableMetrics = false,
            enableDataMasking = false,
            customAttributes = mapOf("test" to "true")
        )
    }
    
    /**
     * 创建测试用的协议上下文
     */
    fun createTestContext(
        protocol: Protocol = Protocol.HTTP,
        source: String = "127.0.0.1:12345",
        destination: String = "example.com:80"
    ): ProtocolContext {
        return ProtocolContext.create(
            protocol = protocol,
            source = source,
            destination = destination,
            sessionAttributes = mapOf("test" to "true"),
            contextMetadata = mapOf("test_context" to "true")
        )
    }
    
    /**
     * 创建测试用的HTTP请求数据包
     */
    fun createHttpRequestPacket(): PacketData {
        val httpRequest = """
            GET /api/test HTTP/1.1
            Host: example.com
            User-Agent: TestAgent/1.0
            Accept: application/json
            
        """.trimIndent()
        
        return PacketData.fromString(
            httpRequest,
            PacketData.PacketDirection.CLIENT_TO_SERVER,
            mapOf("test" to "true")
        )
    }
    
    /**
     * 创建测试用的HTTP响应数据包
     */
    fun createHttpResponsePacket(): PacketData {
        val httpResponse = """
            HTTP/1.1 200 OK
            Content-Type: application/json
            Content-Length: 25
            
            {"message": "Hello Test"}
        """.trimIndent()
        
        return PacketData.fromString(
            httpResponse,
            PacketData.PacketDirection.SERVER_TO_CLIENT,
            mapOf("test" to "true")
        )
    }
    
    /**
     * 创建测试用的WebSocket握手请求
     */
    fun createWebSocketHandshakePacket(): PacketData {
        val wsHandshake = """
            GET /websocket HTTP/1.1
            Host: example.com
            Upgrade: websocket
            Connection: Upgrade
            Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==
            Sec-WebSocket-Version: 13
            
        """.trimIndent()
        
        return PacketData.fromString(
            wsHandshake,
            PacketData.PacketDirection.CLIENT_TO_SERVER,
            mapOf("websocket" to "true")
        )
    }
    
    /**
     * 创建测试用的Mock规则
     */
    fun createTestMockRule(): MockRule {
        return MockRule.urlMatches(
            name = "测试Mock规则",
            urlPattern = "/api/test",
            responseData = """{"test": true, "message": "Mock响应"}""",
            statusCode = 200,
            headers = mapOf("Content-Type" to "application/json")
        )
    }
    
    /**
     * 等待指定时间（毫秒）
     */
    fun waitFor(millis: Long) {
        Thread.sleep(millis)
    }
    
    /**
     * 重试执行直到成功或超时
     */
    fun <T> retryUntil(
        timeoutMs: Long = 5000,
        intervalMs: Long = 100,
        action: () -> T?
    ): T? {
        val startTime = System.currentTimeMillis()
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            val result = try {
                action()
            } catch (e: Exception) {
                null
            }
            
            if (result != null) {
                return result
            }
            
            Thread.sleep(intervalMs)
        }
        
        return null
    }
}
