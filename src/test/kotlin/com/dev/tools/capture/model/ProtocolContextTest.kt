package com.dev.tools.capture.model

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * ProtocolContext测试类
 */
class ProtocolContextTest {
    
    @Test
    fun `测试创建协议上下文`() {
        val context = ProtocolContext.create(
            protocol = Protocol.HTTP,
            source = "192.168.1.100:12345",
            destination = "example.com:80"
        )
        
        assertEquals(Protocol.HTTP, context.protocol)
        assertEquals("192.168.1.100:12345", context.session.source)
        assertEquals("example.com:80", context.session.destination)
        assertNotNull(context.session.id)
        assertFalse(context.sslEnabled)
        assertTrue(context.metadata.isEmpty())
    }
    
    @Test
    fun `测试HTTPS协议SSL启用`() {
        val context = ProtocolContext.create(
            protocol = Protocol.HTTPS,
            source = "client",
            destination = "server"
        )
        
        assertTrue(context.sslEnabled)
        assertEquals(Protocol.HTTPS, context.protocol)
    }
    
    @Test
    fun `测试WSS协议SSL启用`() {
        val context = ProtocolContext.create(
            protocol = Protocol.WSS,
            source = "client",
            destination = "server"
        )
        
        assertTrue(context.sslEnabled)
        assertEquals(Protocol.WSS, context.protocol)
    }
    
    @Test
    fun `测试MQTTS协议SSL启用`() {
        val context = ProtocolContext.create(
            protocol = Protocol.MQTTS,
            source = "client",
            destination = "server"
        )
        
        assertTrue(context.sslEnabled)
        assertEquals(Protocol.MQTTS, context.protocol)
    }
    
    @Test
    fun `测试会话属性`() {
        val sessionAttributes = mapOf(
            "user_id" to "12345",
            "session_type" to "test"
        )
        
        val context = ProtocolContext.create(
            protocol = Protocol.HTTP,
            source = "client",
            destination = "server",
            sessionAttributes = sessionAttributes
        )
        
        assertEquals("12345", context.session.attributes["user_id"])
        assertEquals("test", context.session.attributes["session_type"])
        assertEquals(2, context.session.attributes.size)
    }
    
    @Test
    fun `测试上下文元数据`() {
        val contextMetadata = mapOf(
            "request_id" to "req-123",
            "trace_id" to "trace-456"
        )
        
        val context = ProtocolContext.create(
            protocol = Protocol.HTTP,
            source = "client",
            destination = "server",
            contextMetadata = contextMetadata
        )
        
        assertEquals("req-123", context.metadata["request_id"])
        assertEquals("trace-456", context.metadata["trace_id"])
        assertEquals(2, context.metadata.size)
    }
    
    @Test
    fun `测试上下文复制`() {
        val originalContext = ProtocolContext.create(
            protocol = Protocol.HTTP,
            source = "client",
            destination = "server",
            contextMetadata = mapOf("original" to "true")
        )
        
        val copiedContext = originalContext.copy(
            metadata = originalContext.metadata + ("copied" to "true")
        )
        
        assertEquals(originalContext.protocol, copiedContext.protocol)
        assertEquals(originalContext.session.id, copiedContext.session.id)
        assertEquals("true", copiedContext.metadata["original"])
        assertEquals("true", copiedContext.metadata["copied"])
        assertEquals(2, copiedContext.metadata.size)
    }
    
    @Test
    fun `测试会话ID唯一性`() {
        val context1 = ProtocolContext.create(
            protocol = Protocol.HTTP,
            source = "client1",
            destination = "server1"
        )
        
        val context2 = ProtocolContext.create(
            protocol = Protocol.HTTP,
            source = "client2",
            destination = "server2"
        )
        
        assertNotEquals(context1.session.id, context2.session.id)
    }
    
    @Test
    fun `测试会话时间戳`() {
        val beforeCreation = System.currentTimeMillis()
        val context = ProtocolContext.create(
            protocol = Protocol.HTTP,
            source = "client",
            destination = "server"
        )
        val afterCreation = System.currentTimeMillis()
        
        assertTrue(context.session.startTime.toEpochMilli() >= beforeCreation)
        assertTrue(context.session.startTime.toEpochMilli() <= afterCreation)
    }
    
    @Test
    fun `测试协议枚举属性`() {
        assertTrue(Protocol.HTTP.isSecure.not())
        assertTrue(Protocol.HTTPS.isSecure)
        assertTrue(Protocol.WEBSOCKET.isSecure.not())
        assertTrue(Protocol.WSS.isSecure)
        assertTrue(Protocol.MQTT.isSecure.not())
        assertTrue(Protocol.MQTTS.isSecure)
        
        assertEquals(80, Protocol.HTTP.defaultPort)
        assertEquals(443, Protocol.HTTPS.defaultPort)
        assertEquals(80, Protocol.WEBSOCKET.defaultPort)
        assertEquals(443, Protocol.WSS.defaultPort)
        assertEquals(1883, Protocol.MQTT.defaultPort)
        assertEquals(8883, Protocol.MQTTS.defaultPort)
    }
}
