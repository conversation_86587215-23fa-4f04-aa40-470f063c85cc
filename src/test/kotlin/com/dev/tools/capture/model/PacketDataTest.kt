package com.dev.tools.capture.model

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * PacketData测试类
 */
class PacketDataTest {
    
    @Test
    fun `测试创建PacketData`() {
        val testData = "Hello, World!".toByteArray()
        val metadata = mapOf("test" to "value")
        
        val packet = PacketData(
            rawData = testData,
            direction = PacketData.PacketDirection.CLIENT_TO_SERVER,
            metadata = metadata
        )
        
        assertNotNull(packet.id)
        assertArrayEquals(testData, packet.rawData)
        assertEquals(PacketData.PacketDirection.CLIENT_TO_SERVER, packet.direction)
        assertEquals(metadata, packet.metadata)
        assertEquals(testData.size, packet.size)
        assertTrue(packet.timestamp.toEpochMilli() > 0)
    }
    
    @Test
    fun `测试从字符串创建PacketData`() {
        val testString = "Test packet data"
        val packet = PacketData.fromString(testString)
        
        assertEquals(testString, packet.rawDataAsString)
        assertEquals(testString.length, packet.size)
        assertEquals(PacketData.PacketDirection.UNKNOWN, packet.direction)
    }
    
    @Test
    fun `测试客户端到服务器数据包`() {
        val testData = "Client request".toByteArray()
        val packet = PacketData.clientToServer(testData)
        
        assertEquals(PacketData.PacketDirection.CLIENT_TO_SERVER, packet.direction)
        assertArrayEquals(testData, packet.rawData)
    }
    
    @Test
    fun `测试服务器到客户端数据包`() {
        val testData = "Server response".toByteArray()
        val packet = PacketData.serverToClient(testData)
        
        assertEquals(PacketData.PacketDirection.SERVER_TO_CLIENT, packet.direction)
        assertArrayEquals(testData, packet.rawData)
    }
    
    @Test
    fun `测试数据包方向枚举`() {
        assertEquals("CLIENT_TO_SERVER", PacketData.PacketDirection.CLIENT_TO_SERVER.name)
        assertEquals("SERVER_TO_CLIENT", PacketData.PacketDirection.SERVER_TO_CLIENT.name)
        assertEquals("UNKNOWN", PacketData.PacketDirection.UNKNOWN.name)
    }
    
    @Test
    fun `测试数据包ID唯一性`() {
        val packet1 = PacketData.fromString("test1")
        val packet2 = PacketData.fromString("test2")
        
        assertNotEquals(packet1.id, packet2.id)
    }
    
    @Test
    fun `测试空数据包`() {
        val packet = PacketData(
            rawData = ByteArray(0),
            direction = PacketData.PacketDirection.UNKNOWN
        )
        
        assertEquals(0, packet.size)
        assertEquals("", packet.rawDataAsString)
        assertTrue(packet.rawData.isEmpty())
    }
    
    @Test
    fun `测试大数据包`() {
        val largeData = ByteArray(1024 * 1024) { it.toByte() } // 1MB
        val packet = PacketData(
            rawData = largeData,
            direction = PacketData.PacketDirection.CLIENT_TO_SERVER
        )
        
        assertEquals(1024 * 1024, packet.size)
        assertArrayEquals(largeData, packet.rawData)
    }
    
    @Test
    fun `测试解析数据设置`() {
        val packet = PacketData.fromString("test")
        assertNull(packet.parsedData)
        
        val packetWithParsed = packet.copy(parsedData = "parsed content")
        assertEquals("parsed content", packetWithParsed.parsedData)
    }
    
    @Test
    fun `测试元数据操作`() {
        val initialMetadata = mapOf("key1" to "value1")
        val packet = PacketData.fromString("test", metadata = initialMetadata)
        
        assertEquals("value1", packet.metadata["key1"])
        assertEquals(1, packet.metadata.size)
        
        val updatedMetadata = packet.metadata + ("key2" to "value2")
        val updatedPacket = packet.copy(metadata = updatedMetadata)
        
        assertEquals(2, updatedPacket.metadata.size)
        assertEquals("value1", updatedPacket.metadata["key1"])
        assertEquals("value2", updatedPacket.metadata["key2"])
    }
}
