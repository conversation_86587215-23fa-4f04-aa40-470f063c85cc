package com.dev.tools.capture.model

import com.dev.tools.capture.TestConfig
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * MockRule测试类
 */
class MockRuleTest {
    
    @Test
    fun `测试URL匹配Mock规则`() {
        val rule = MockRule.urlMatches(
            name = "测试规则",
            urlPattern = "/api/test",
            responseData = """{"success": true}""",
            statusCode = 200
        )
        
        assertEquals("测试规则", rule.name)
        assertTrue(rule.enabled)
        assertEquals(0, rule.priority)
        
        // 测试匹配
        val matchingPacket = TestConfig.createHttpRequestPacket()
        val context = TestConfig.createTestContext()
        
        assertTrue(rule.matches(matchingPacket, context))
        
        // 测试响应生成
        val response = rule.generateResponse(matchingPacket, context)
        assertTrue(response.rawDataAsString.contains("HTTP/1.1 200 OK"))
        assertTrue(response.rawDataAsString.contains("""{"success": true}"""))
    }
    
    @Test
    fun `测试URL不匹配`() {
        val rule = MockRule.urlMatches(
            name = "测试规则",
            urlPattern = "/api/other",
            responseData = """{"success": true}""",
            statusCode = 200
        )
        
        val packet = TestConfig.createHttpRequestPacket() // 包含 /api/test
        val context = TestConfig.createTestContext()
        
        assertFalse(rule.matches(packet, context))
    }
    
    @Test
    fun `测试自定义条件Mock规则`() {
        val rule = MockRule(
            name = "自定义条件规则",
            description = "测试自定义条件",
            enabled = true,
            priority = 10,
            condition = { packet, _ ->
                packet.rawDataAsString.contains("User-Agent: TestAgent")
            },
            response = { _, _ ->
                PacketData.fromString("Custom response")
            }
        )
        
        val packet = TestConfig.createHttpRequestPacket()
        val context = TestConfig.createTestContext()
        
        assertTrue(rule.matches(packet, context))
        
        val response = rule.generateResponse(packet, context)
        assertEquals("Custom response", response.rawDataAsString)
    }
    
    @Test
    fun `测试规则优先级`() {
        val lowPriorityRule = MockRule.urlMatches(
            name = "低优先级",
            urlPattern = "/api/test",
            responseData = "low"
        ).update(priority = 1)

        val highPriorityRule = MockRule.urlMatches(
            name = "高优先级",
            urlPattern = "/api/test",
            responseData = "high"
        ).update(priority = 10)
        
        assertTrue(highPriorityRule.priority > lowPriorityRule.priority)
    }
    
    @Test
    fun `测试规则启用禁用`() {
        val rule = MockRule.urlMatches(
            name = "测试规则",
            urlPattern = "/api/test",
            responseData = "response"
        )
        
        assertTrue(rule.enabled)
        
        val disabledRule = rule.update(enabled = false)
        assertFalse(disabledRule.enabled)
        assertEquals(rule.name, disabledRule.name)
        assertEquals(rule.id, disabledRule.id)
    }
    
    @Test
    fun `测试规则更新`() {
        val originalRule = MockRule.urlMatches(
            name = "原始规则",
            urlPattern = "/api/test",
            responseData = "original"
        )
        
        val updatedRule = originalRule.update(
            name = "更新规则",
            description = "更新描述",
            priority = 5,
            enabled = false
        )
        
        assertEquals("更新规则", updatedRule.name)
        assertEquals("更新描述", updatedRule.description)
        assertEquals(5, updatedRule.priority)
        assertFalse(updatedRule.enabled)
        assertEquals(originalRule.id, updatedRule.id) // ID应该保持不变
    }
    
    @Test
    fun `测试规则ID唯一性`() {
        val rule1 = MockRule.urlMatches("规则1", "/api/1", "response1")
        val rule2 = MockRule.urlMatches("规则2", "/api/2", "response2")
        
        assertNotEquals(rule1.id, rule2.id)
    }
    
    @Test
    fun `测试规则元数据`() {
        val metadata = mapOf(
            "author" to "test",
            "version" to "1.0"
        )
        
        val rule = MockRule(
            name = "带元数据的规则",
            condition = { _, _ -> true },
            response = { _, _ -> PacketData.fromString("response") },
            metadata = metadata
        )
        
        assertEquals("test", rule.metadata["author"])
        assertEquals("1.0", rule.metadata["version"])
    }
    
    @Test
    fun `测试HTTP响应格式`() {
        val rule = MockRule.urlMatches(
            name = "HTTP响应测试",
            urlPattern = "/api/test",
            responseData = """{"message": "hello"}""",
            statusCode = 201,
            headers = mapOf(
                "Content-Type" to "application/json",
                "X-Custom" to "test-value"
            )
        )
        
        val packet = TestConfig.createHttpRequestPacket()
        val context = TestConfig.createTestContext()
        val response = rule.generateResponse(packet, context)
        
        val responseText = response.rawDataAsString
        assertTrue(responseText.contains("HTTP/1.1 201"))
        assertTrue(responseText.contains("Content-Type: application/json"))
        assertTrue(responseText.contains("X-Custom: test-value"))
        assertTrue(responseText.contains("""{"message": "hello"}"""))
    }
}
