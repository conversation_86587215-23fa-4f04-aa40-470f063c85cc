#!/usr/bin/env python3
"""
专门测试NetworkTrafficAnalyzer统计功能
"""

import socket
import time
import requests
import threading
from urllib.parse import urlparse

# 代理配置
PROXY_HOST = '127.0.0.1'
PROXY_PORT = 8888

def test_http_requests():
    """测试HTTP请求统计"""
    print("\n=== 测试HTTP请求统计 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    # 测试HTTP请求（应该能正常工作）
    http_urls = [
        'http://httpbin.org/get',
        'http://httpbin.org/headers',
        'http://httpbin.org/user-agent'
    ]
    
    for url in http_urls:
        try:
            print(f"📡 HTTP请求: {url}")
            response = requests.get(url, proxies=proxies, timeout=10)
            print(f"✅ 状态码: {response.status_code}, 大小: {len(response.content)} 字节")
        except Exception as e:
            print(f"❌ HTTP请求失败: {e}")
        time.sleep(1)

def test_connect_requests():
    """测试CONNECT请求统计"""
    print("\n=== 测试CONNECT请求统计 ===")
    
    test_domains = [
        'httpbin.org',
        'www.google.com',
        'api.github.com',
        'www.baidu.com'
    ]
    
    for domain in test_domains:
        try:
            print(f"🔗 CONNECT请求: {domain}:443")
            
            # 连接到代理
            proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            proxy_socket.settimeout(10)
            proxy_socket.connect((PROXY_HOST, PROXY_PORT))
            
            # 发送CONNECT请求
            connect_request = f"CONNECT {domain}:443 HTTP/1.1\r\n"
            connect_request += f"Host: {domain}:443\r\n"
            connect_request += "User-Agent: TrafficAnalyzerTest/1.0\r\n"
            connect_request += "Proxy-Connection: keep-alive\r\n"
            connect_request += "\r\n"
            
            proxy_socket.send(connect_request.encode())
            
            # 读取代理响应
            response = proxy_socket.recv(1024).decode()
            print(f"代理响应: {response.strip()}")
            
            proxy_socket.close()
            
        except Exception as e:
            print(f"❌ CONNECT失败: {e}")
        
        time.sleep(1)

def test_post_requests():
    """测试POST请求统计"""
    print("\n=== 测试POST请求统计 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    # 测试POST请求
    test_data = {
        'test': 'traffic_analyzer',
        'timestamp': int(time.time()),
        'data': 'This is test data for NetworkTrafficAnalyzer'
    }
    
    try:
        print("📤 POST请求: http://httpbin.org/post")
        response = requests.post(
            'http://httpbin.org/post', 
            json=test_data, 
            proxies=proxies, 
            timeout=10
        )
        print(f"✅ POST响应: {response.status_code}, 大小: {len(response.content)} 字节")
        
    except Exception as e:
        print(f"❌ POST请求失败: {e}")

def test_multiple_requests():
    """测试多个请求的统计"""
    print("\n=== 测试多个请求统计 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    urls = [
        'http://httpbin.org/json',
        'http://httpbin.org/xml',
        'http://httpbin.org/html',
        'http://httpbin.org/robots.txt',
        'http://httpbin.org/status/200'
    ]
    
    for i, url in enumerate(urls, 1):
        try:
            print(f"📊 请求 {i}/5: {url}")
            response = requests.get(url, proxies=proxies, timeout=10)
            print(f"✅ 响应: {response.status_code}")
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        time.sleep(0.5)

def test_concurrent_requests():
    """测试并发请求统计"""
    print("\n=== 测试并发请求统计 ===")
    
    def worker(worker_id, url):
        try:
            proxies = {
                'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
                'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
            }
            
            print(f"🔄 Worker {worker_id}: {url}")
            response = requests.get(url, proxies=proxies, timeout=15)
            print(f"✅ Worker {worker_id}: {response.status_code}")
            
        except Exception as e:
            print(f"❌ Worker {worker_id}: {e}")
    
    urls = [
        'http://httpbin.org/delay/1',
        'http://httpbin.org/delay/2',
        'http://httpbin.org/delay/1',
    ]
    
    threads = []
    for i, url in enumerate(urls):
        thread = threading.Thread(target=worker, args=(i+1, url))
        threads.append(thread)
        thread.start()
        time.sleep(0.2)  # 错开启动
    
    for thread in threads:
        thread.join()

def test_large_requests():
    """测试大请求的统计"""
    print("\n=== 测试大请求统计 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    # 测试不同大小的响应
    sizes = [1024, 10240, 102400]  # 1KB, 10KB, 100KB
    
    for size in sizes:
        try:
            url = f'http://httpbin.org/bytes/{size}'
            print(f"📦 请求大数据: {size} 字节")
            response = requests.get(url, proxies=proxies, timeout=15)
            print(f"✅ 收到: {len(response.content)} 字节")
        except Exception as e:
            print(f"❌ 大数据请求失败: {e}")
        time.sleep(1)

if __name__ == "__main__":
    print("🚀 开始NetworkTrafficAnalyzer统计功能测试")
    print(f"代理地址: {PROXY_HOST}:{PROXY_PORT}")
    print("=" * 60)
    
    # 等待代理启动
    time.sleep(2)
    
    # 测试HTTP请求统计
    test_http_requests()
    
    # 测试CONNECT请求统计
    test_connect_requests()
    
    # 测试POST请求统计
    test_post_requests()
    
    # 测试多个请求统计
    test_multiple_requests()
    
    # 测试并发请求统计
    test_concurrent_requests()
    
    # 测试大请求统计
    test_large_requests()
    
    print("\n" + "=" * 60)
    print("🎯 NetworkTrafficAnalyzer测试完成！")
    print("\n请检查PacketCapture日志中的统计信息：")
    print("1. 📊 HTTP请求和响应的统计")
    print("2. 🔗 CONNECT请求的统计")
    print("3. 📤 POST请求的统计")
    print("4. 🔄 并发请求的统计")
    print("5. 📦 大数据传输的统计")
    print("6. 📈 总体流量统计数据")
