#!/usr/bin/env python3
"""
测试SSL连接的脚本
用于验证PacketCapture的SSL中间人解密是否正常工作
"""

import ssl
import socket
import requests
import urllib3
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
urllib3.disable_warnings(InsecureRequestWarning)

def test_direct_ssl_connection():
    """测试直接SSL连接"""
    print("=== 测试直接SSL连接 ===")
    
    try:
        # 创建SSL上下文，禁用证书验证
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        # 通过代理连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        # 连接到代理
        sock.connect(('127.0.0.1', 8888))
        
        # 发送CONNECT请求
        connect_request = f"CONNECT qa.kanzhun-inc.com:443 HTTP/1.1\r\nHost: qa.kanzhun-inc.com:443\r\n\r\n"
        sock.send(connect_request.encode())
        
        # 读取代理响应
        response = sock.recv(1024).decode()
        print(f"代理响应: {response.strip()}")
        
        if "200" in response:
            # 建立SSL连接
            ssl_sock = context.wrap_socket(sock, server_hostname='qa.kanzhun-inc.com')
            print(f"SSL连接成功！")
            print(f"SSL版本: {ssl_sock.version()}")
            print(f"加密套件: {ssl_sock.cipher()}")
            
            # 获取证书信息
            cert = ssl_sock.getpeercert()
            if cert:
                print(f"证书主题: {cert.get('subject', 'N/A')}")
                print(f"证书颁发者: {cert.get('issuer', 'N/A')}")
                print(f"证书SAN: {cert.get('subjectAltName', 'N/A')}")
            
            # 发送HTTP请求
            http_request = "GET /api/test HTTP/1.1\r\nHost: qa.kanzhun-inc.com\r\nConnection: close\r\n\r\n"
            ssl_sock.send(http_request.encode())
            
            # 读取响应
            response = ssl_sock.recv(4096).decode()
            print(f"HTTP响应: {response[:200]}...")
            
            ssl_sock.close()
        else:
            print("代理连接失败")
            sock.close()
            
    except Exception as e:
        print(f"连接失败: {e}")

def test_requests_with_proxy():
    """使用requests库测试代理连接"""
    print("\n=== 测试requests库代理连接 ===")
    
    try:
        proxies = {
            'http': 'http://127.0.0.1:8888',
            'https': 'http://127.0.0.1:8888'
        }
        
        # 禁用SSL验证
        response = requests.get(
            'https://qa.kanzhun-inc.com/api/test',
            proxies=proxies,
            verify=False,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text[:200]}...")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_direct_ssl_connection()
    test_requests_with_proxy()
