#!/usr/bin/env python3
"""
测试证书验证的脚本
用于验证PacketCapture在SSL握手中发送的证书
"""

import ssl
import socket
import hashlib
import base64
from cryptography import x509
from cryptography.hazmat.backends import default_backend

def get_certificate_info(hostname, port=443, proxy_host='127.0.0.1', proxy_port=8888):
    """获取通过代理连接时服务器发送的证书信息"""
    print(f"=== 获取 {hostname}:{port} 的证书信息 ===")
    
    try:
        # 创建socket连接到代理
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((proxy_host, proxy_port))
        
        # 发送CONNECT请求
        connect_request = f"CONNECT {hostname}:{port} HTTP/1.1\r\nHost: {hostname}:{port}\r\n\r\n"
        sock.send(connect_request.encode())
        
        # 读取代理响应
        response = sock.recv(1024).decode()
        print(f"代理响应: {response.strip()}")
        
        if "200" not in response:
            print("代理连接失败")
            return None
            
        # 创建SSL上下文，不验证证书但获取证书信息
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        # 建立SSL连接
        ssl_sock = context.wrap_socket(sock, server_hostname=hostname)
        print(f"SSL连接成功！")
        
        # 获取证书
        der_cert = ssl_sock.getpeercert(binary_form=True)
        if der_cert:
            # 解析证书
            cert = x509.load_der_x509_certificate(der_cert, default_backend())
            
            # 计算证书指纹
            fingerprint = hashlib.sha256(der_cert).digest()
            fingerprint_b64 = base64.b64encode(fingerprint).decode()
            
            print(f"证书指纹 (SHA256): {fingerprint_b64}")
            print(f"证书主题: {cert.subject}")
            print(f"证书颁发者: {cert.issuer}")
            
            # 获取SAN扩展
            try:
                san_ext = cert.extensions.get_extension_for_oid(x509.oid.ExtensionOID.SUBJECT_ALTERNATIVE_NAME)
                san_names = [name.value for name in san_ext.value]
                print(f"证书SAN: {san_names}")
            except x509.ExtensionNotFound:
                print("证书SAN: []")
            
            print(f"证书有效期: {cert.not_valid_before} - {cert.not_valid_after}")
            
            # 检查是否是根证书
            if "PacketCapture Root CA" in str(cert.subject):
                print("⚠️  警告：收到的是根证书，不是服务器证书！")
                return False
            elif hostname in str(cert.subject):
                print("✅ 正确：收到的是服务器证书")
                return True
            else:
                print("❓ 未知：收到的证书类型不明确")
                return None
        
        ssl_sock.close()
        
    except Exception as e:
        print(f"连接失败: {e}")
        return None

def test_multiple_domains():
    """测试多个域名的证书"""
    domains = [
        'qa.kanzhun-inc.com',
        'api.example.com',
        'test.example.org'
    ]
    
    results = {}
    for domain in domains:
        print(f"\n{'='*50}")
        result = get_certificate_info(domain)
        results[domain] = result
        print(f"{'='*50}")
    
    print(f"\n=== 测试结果汇总 ===")
    for domain, result in results.items():
        status = "✅ 正确" if result else "❌ 错误" if result is False else "❓ 未知"
        print(f"{domain}: {status}")

if __name__ == "__main__":
    test_multiple_domains()
