#!/usr/bin/env kotlin

/**
 * 测试SSL中间人解密配置修复
 * 
 * 测试场景：
 * 1. enableSslMitm = false 时，应该直接使用隧道模式
 * 2. enableSslMitm = true 时，尝试SSL解密，失败时降级到隧道模式
 */

import com.dev.tools.capture.PacketCaptureSDK
import com.dev.tools.capture.model.*
import java.util.concurrent.CountDownLatch
import kotlin.system.exitProcess

fun main() {
    println("🧪 测试SSL中间人解密配置修复")
    println("=" * 50)
    
    // 测试场景1: enableSslMitm = false
    testSslMitmDisabled()
    
    // 等待一段时间
    Thread.sleep(2000)
    
    // 测试场景2: enableSslMitm = true
    testSslMitmEnabled()
}

/**
 * 测试场景1: SSL中间人解密禁用
 */
fun testSslMitmDisabled() {
    println("\n📋 测试场景1: enableSslMitm = false")
    println("预期行为: 所有HTTPS请求应该直接使用隧道模式")
    
    val config = SDKConfig(
        port = 8890,
        protocols = setOf(Protocol.HTTP, Protocol.HTTPS),
        enableSslMitm = false,  // 禁用SSL中间人解密
        sslFallbackStrategy = FallbackStrategyType.TUNNEL,
        
        // 最小化配置
        sslCertificatePath = "./temp/test1/ssl/certificates",
        sslKeyStorePath = "./temp/test1/ssl/keystore.p12",
        sslKeyStorePassword = "test123",
        enableCertificateDownload = false,
        
        storageConfig = StorageConfig.MEMORY,
        enableLogging = true,
        logLevel = LogLevel.INFO,
        enableMetrics = false
    )
    
    try {
        val sdk = PacketCaptureSDK(config)
        
        println("✅ 启动代理服务器 (端口: ${config.port})")
        sdk.start()
        
        println("🔍 测试配置:")
        println("   - enableSslMitm: ${config.enableSslMitm}")
        println("   - 预期行为: 直接使用隧道模式")
        println("   - 测试方法: 手动配置浏览器代理到 127.0.0.1:${config.port}")
        println("   - 访问 https://www.baidu.com 应该正常工作")
        
        // 运行30秒
        Thread.sleep(30000)
        
        sdk.stop()
        println("✅ 测试场景1完成")
        
    } catch (e: Exception) {
        println("❌ 测试场景1失败: ${e.message}")
        e.printStackTrace()
    }
}

/**
 * 测试场景2: SSL中间人解密启用
 */
fun testSslMitmEnabled() {
    println("\n📋 测试场景2: enableSslMitm = true")
    println("预期行为: 尝试SSL解密，失败时自动降级到隧道模式")
    
    val config = SDKConfig(
        port = 8891,
        protocols = setOf(Protocol.HTTP, Protocol.HTTPS),
        enableSslMitm = true,   // 启用SSL中间人解密
        sslFallbackStrategy = FallbackStrategyType.TUNNEL,
        
        // SSL证书配置
        sslCertificatePath = "./temp/test2/ssl/certificates",
        sslKeyStorePath = "./temp/test2/ssl/keystore.p12",
        sslKeyStorePassword = "test123",
        enableCertificateDownload = true,
        certificateDownloadPort = 8892,
        
        storageConfig = StorageConfig.MEMORY,
        enableLogging = true,
        logLevel = LogLevel.INFO,
        enableMetrics = false
    )
    
    try {
        val sdk = PacketCaptureSDK(config)
        
        println("✅ 启动代理服务器 (端口: ${config.port})")
        sdk.start()
        
        println("🔍 测试配置:")
        println("   - enableSslMitm: ${config.enableSslMitm}")
        println("   - 预期行为: 尝试SSL解密，失败时降级")
        println("   - 测试方法: 手动配置浏览器代理到 127.0.0.1:${config.port}")
        println("   - 访问 https://www.baidu.com")
        println("   - 如果未安装证书，应该看到降级到隧道模式的日志")
        
        // 运行30秒
        Thread.sleep(30000)
        
        sdk.stop()
        println("✅ 测试场景2完成")
        
    } catch (e: Exception) {
        println("❌ 测试场景2失败: ${e.message}")
        e.printStackTrace()
    }
}

/**
 * 扩展函数：重复字符串
 */
private operator fun String.times(n: Int): String = this.repeat(n)
