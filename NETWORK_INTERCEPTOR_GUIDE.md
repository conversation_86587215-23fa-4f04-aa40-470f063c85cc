# 网络请求拦截器使用指南

## 🎯 概述

PacketCapture SDK 网络请求拦截器是一个功能强大的HTTP/HTTPS代理服务器，能够：

- 🌐 拦截和分析真实的网络流量
- 🔒 解密HTTPS流量（通过SSL中间人攻击）
- 🎯 智能Mock规则引擎
- 📊 实时流量统计和分析
- 🛡️ 降级处理策略

## 🚀 快速开始

### 1. 启动拦截器

```bash
./gradlew run
```

启动后会看到类似输出：

```
╔══════════════════════════════════════════════════════════════╗
║                    PacketCapture SDK                         ║
║                   网络请求拦截器                              ║
║                                                              ║
║  🌐 支持 HTTP/HTTPS/WebSocket 协议                           ║
║  🔒 SSL/TLS 中间人解密                                       ║
║  🎯 智能Mock规则引擎                                         ║
║  📊 实时流量分析                                             ║
║  🛡️  降级处理策略                                            ║
╚══════════════════════════════════════════════════════════════╝

✅ 代理服务器已启动!
🔍 实时流量监控中...
```

### 2. 配置设备代理

#### Windows
1. 打开 **设置** > **网络和Internet** > **代理**
2. 启用 **使用代理服务器**
3. 地址: `127.0.0.1`  端口: `8888`

#### macOS
1. 打开 **系统偏好设置** > **网络**
2. 选择网络连接 > **高级** > **代理**
3. 勾选 **Web代理(HTTP)** 和 **安全Web代理(HTTPS)**
4. 服务器: `127.0.0.1`  端口: `8888`

#### Android
1. 长按WiFi网络 > **修改网络**
2. **高级选项** > **代理** > **手动**
3. 主机名: `127.0.0.1`  端口: `8888`

#### iOS
1. **设置** > **WiFi** > 点击网络名称旁的 **'i'**
2. **配置代理** > **手动**
3. 服务器: `127.0.0.1`  端口: `8888`

### 3. 开始拦截

配置完代理后，在设备上：
- 打开浏览器访问任何网站
- 使用任何App进行网络请求
- 查看控制台实时显示的流量分析

## 📊 流量分析功能

### 实时监控显示

```
📤 [HTTP] GET example.com/api/users
   📱 User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)...
✅ [200] OK (1234 bytes)
   📄 Content-Type: application/json

📤 [HTTPS] POST api.github.com/user
⚠️ [404] Not Found (567 bytes)
   📄 Content-Type: text/html
```

### 统计信息

每100个响应会显示统计信息：

```
📊 流量统计 (请求: 150, 响应: 145)
🌐 热门域名:
   api.github.com: 45 次
   www.google.com: 32 次
   cdn.jsdelivr.net: 18 次
🔧 HTTP方法:
   GET: 120 次
   POST: 25 次
   PUT: 5 次
📈 状态码:
   200: 98 次
   404: 25 次
   500: 12 次
```

## 🎯 Mock规则功能

拦截器预设了以下Mock规则：

### 1. 通用API Mock
- **匹配**: `GET /api/(users|user|profile)`
- **响应**: 返回Mock用户数据

### 2. 广告拦截
- **匹配**: 包含 `ads|advertisement|doubleclick|googlesyndication`
- **响应**: 返回204状态码（无内容）

### 3. 登录接口Mock
- **匹配**: `POST /api/login`
- **响应**: 返回Mock登录成功数据

### 4. 错误页面Mock
- **匹配**: URL包含 `/error`
- **响应**: 返回500服务器错误

### 5. 静态页面Mock
- **匹配**: HTML文件请求
- **响应**: 返回自定义HTML页面

## 🔒 HTTPS解密

### SSL证书处理

1. **自动生成**: 首次HTTPS请求时自动生成SSL证书
2. **证书安装**: 需要在设备上安装并信任生成的证书
3. **安全警告**: 浏览器可能显示安全警告，这是正常的

### 降级策略

当SSL解密失败时：
- **隧道模式**: 自动切换到隧道模式，直接转发加密数据
- **继续监控**: 仍然可以监控连接信息，但无法解密内容

## ⚙️ 配置选项

### 修改监听端口

编辑 `Main.kt` 中的配置：

```kotlin
val config = SDKConfig(
    port = 9999,  // 修改端口
    protocols = setOf(Protocol.HTTP, Protocol.HTTPS, Protocol.WEBSOCKET),
    enableSslMitm = true,
    // ... 其他配置
)
```

### 添加自定义Mock规则

```kotlin
val customRule = MockRuleBuilder.create("自定义规则")
    .description("拦截特定API")
    .matchHttpMethod("POST")
    .matchUrlPattern(".*/api/custom.*")
    .respondWithJson("""{"custom": "response"}""")
    .build()

sdk.addMockRule(customRule)
```

### 调整日志级别

```kotlin
val config = SDKConfig(
    // ...
    enableLogging = true,
    logLevel = LogLevel.DEBUG,  // TRACE, DEBUG, INFO, WARN, ERROR
    // ...
)
```

## 🛠️ 故障排除

### 常见问题

1. **无法连接到代理**
   - 检查防火墙设置
   - 确认端口8888未被占用
   - 验证代理配置是否正确

2. **HTTPS网站无法访问**
   - 安装并信任SSL证书
   - 检查SSL解密是否启用
   - 查看控制台错误信息

3. **某些App无法使用代理**
   - 部分App可能绕过系统代理
   - 尝试使用VPN模式（需要额外配置）

### 调试模式

启用详细日志：

```kotlin
val config = SDKConfig(
    // ...
    logLevel = LogLevel.TRACE,
    enableMetrics = true,
    // ...
)
```

## 📝 使用示例

### 测试API拦截

1. 启动拦截器
2. 配置代理
3. 访问: `http://httpbin.org/get`
4. 观察控制台输出

### 测试Mock功能

1. 访问: `http://example.com/api/users`
2. 应该看到Mock响应而不是真实API响应

### 测试广告拦截

1. 访问包含广告的网站
2. 观察广告请求被拦截（204响应）

## 🔧 高级功能

### 自定义流量分析器

```kotlin
class CustomAnalyzer : PacketCallback {
    override fun onPacketReceived(packet: PacketData, context: ProtocolContext) {
        // 自定义分析逻辑
        if (packet.rawDataAsString.contains("sensitive")) {
            println("⚠️ 检测到敏感数据")
        }
    }
    
    override fun onError(exception: CaptureException, context: ProtocolContext?) {
        println("❌ 分析错误: ${exception.message}")
    }
}
```

### 动态Mock规则

```kotlin
// 运行时添加规则
val dynamicRule = MockRuleBuilder.create("动态规则")
    .matchUrlPattern(".*/dynamic.*")
    .respondWith { packet, context ->
        val timestamp = System.currentTimeMillis()
        PacketData.fromString("""{"timestamp": $timestamp}""")
    }
    .build()

sdk.addMockRule(dynamicRule)
```

## 📋 注意事项

1. **仅用于开发和测试**: 不要在生产环境使用
2. **隐私保护**: 拦截的数据可能包含敏感信息
3. **法律合规**: 确保符合当地法律法规
4. **性能影响**: 代理会增加网络延迟
5. **证书安全**: 妥善保管生成的SSL证书

## 🆘 获取帮助

如果遇到问题：

1. 查看控制台错误信息
2. 检查日志文件: `logs/packet-capture-sdk.log`
3. 确认网络配置正确
4. 尝试重启拦截器

---

**PacketCapture SDK** - 专业的网络流量分析工具
