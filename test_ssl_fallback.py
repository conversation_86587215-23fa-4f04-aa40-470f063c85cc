#!/usr/bin/env python3
"""
测试SSL降级逻辑和CONNECT请求统计
"""

import socket
import ssl
import time
import threading
from urllib.parse import urlparse

# 代理配置
PROXY_HOST = '127.0.0.1'
PROXY_PORT = 8888

def test_connect_request(host, port=443):
    """测试CONNECT请求"""
    print(f"\n=== 测试CONNECT请求: {host}:{port} ===")
    
    try:
        # 连接到代理
        proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        proxy_socket.settimeout(10)
        proxy_socket.connect((PROXY_HOST, PROXY_PORT))
        
        # 发送CONNECT请求
        connect_request = f"CONNECT {host}:{port} HTTP/1.1\r\n"
        connect_request += f"Host: {host}:{port}\r\n"
        connect_request += "Proxy-Connection: keep-alive\r\n"
        connect_request += "\r\n"
        
        print(f"发送CONNECT请求: {connect_request.strip()}")
        proxy_socket.send(connect_request.encode())
        
        # 读取代理响应
        response = proxy_socket.recv(1024).decode()
        print(f"代理响应: {response.strip()}")
        
        if "200" in response:
            print("✅ CONNECT请求成功")
            
            # 尝试SSL握手
            try:
                print("尝试SSL握手...")
                ssl_context = ssl.create_default_context()
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                
                ssl_socket = ssl_context.wrap_socket(proxy_socket, server_hostname=host)
                print("✅ SSL握手成功")
                
                # 发送简单的HTTP请求
                http_request = f"GET / HTTP/1.1\r\nHost: {host}\r\nConnection: close\r\n\r\n"
                ssl_socket.send(http_request.encode())
                
                response = ssl_socket.recv(1024).decode()
                print(f"HTTP响应: {response[:100]}...")
                
                ssl_socket.close()
                
            except Exception as e:
                print(f"❌ SSL握手失败: {e}")
                print("💡 应该触发降级到隧道模式")
                
        else:
            print("❌ CONNECT请求失败")
            
        proxy_socket.close()
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")

def test_multiple_domains():
    """测试多个域名"""
    test_domains = [
        "qa.kanzhun-inc.com",
        "api.example.com", 
        "test.example.org",
        "httpbin.org",
        "www.google.com"
    ]
    
    print("=== 测试多个域名的CONNECT请求 ===")
    
    for domain in test_domains:
        test_connect_request(domain)
        time.sleep(1)  # 避免请求过快

def test_concurrent_requests():
    """测试并发请求"""
    print("\n=== 测试并发CONNECT请求 ===")
    
    def worker(domain):
        test_connect_request(domain)
    
    domains = ["httpbin.org", "www.google.com", "api.example.com"]
    threads = []
    
    for domain in domains:
        thread = threading.Thread(target=worker, args=(domain,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()

if __name__ == "__main__":
    print("🚀 开始测试SSL降级逻辑和CONNECT请求统计")
    print(f"代理地址: {PROXY_HOST}:{PROXY_PORT}")
    
    # 等待一下确保代理启动
    time.sleep(2)
    
    # 测试单个域名
    test_connect_request("httpbin.org")
    
    # 测试多个域名
    test_multiple_domains()
    
    # 测试并发请求
    test_concurrent_requests()
    
    print("\n🎯 测试完成！请检查PacketCapture日志：")
    print("1. 是否看到CONNECT请求的统计")
    print("2. SSL握手失败时是否降级到隧道模式")
    print("3. 是否有降级日志：'🔄 SSL解密失败，降级到隧道模式'")
