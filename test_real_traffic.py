#!/usr/bin/env python3
"""
测试真实的网络流量和统计功能
"""

import socket
import ssl
import time
import threading
import requests
from urllib.parse import urlparse

# 代理配置
PROXY_HOST = '127.0.0.1'
PROXY_PORT = 8888

def test_real_http_request():
    """测试真实的HTTP请求"""
    print("\n=== 测试真实HTTP请求 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    test_urls = [
        'http://httpbin.org/get',
        'https://httpbin.org/get',
        'https://www.baidu.com',
        'https://api.github.com/users/octocat'
    ]
    
    for url in test_urls:
        try:
            print(f"\n📡 请求: {url}")
            response = requests.get(url, proxies=proxies, timeout=10, verify=False)
            print(f"✅ 状态码: {response.status_code}")
            print(f"📊 响应大小: {len(response.content)} 字节")
            print(f"🔗 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 请求失败: {e}")
        
        time.sleep(1)

def test_real_https_with_certificate():
    """测试HTTPS请求并检查证书"""
    print("\n=== 测试HTTPS证书验证 ===")
    
    test_domains = [
        'httpbin.org',
        'www.google.com',
        'api.github.com',
        'www.baidu.com'
    ]
    
    for domain in test_domains:
        try:
            print(f"\n🔐 测试域名: {domain}")
            
            # 连接到代理
            proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            proxy_socket.settimeout(15)
            proxy_socket.connect((PROXY_HOST, PROXY_PORT))
            
            # 发送CONNECT请求
            connect_request = f"CONNECT {domain}:443 HTTP/1.1\r\n"
            connect_request += f"Host: {domain}:443\r\n"
            connect_request += "User-Agent: TestClient/1.0\r\n"
            connect_request += "Proxy-Connection: keep-alive\r\n"
            connect_request += "\r\n"
            
            proxy_socket.send(connect_request.encode())
            
            # 读取代理响应
            response = proxy_socket.recv(1024).decode()
            print(f"代理响应: {response.strip()}")
            
            if "200" in response:
                print("✅ CONNECT成功")
                
                # 尝试SSL握手
                try:
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_NONE
                    
                    ssl_socket = ssl_context.wrap_socket(proxy_socket, server_hostname=domain)
                    print("✅ SSL握手成功")
                    
                    # 获取证书信息
                    cert = ssl_socket.getpeercert()
                    if cert:
                        subject = dict(x[0] for x in cert['subject'])
                        print(f"📜 证书主题: {subject.get('commonName', 'N/A')}")
                        print(f"📅 证书有效期: {cert.get('notAfter', 'N/A')}")
                        
                        # 检查SAN
                        san_list = []
                        if 'subjectAltName' in cert:
                            san_list = [name[1] for name in cert['subjectAltName'] if name[0] == 'DNS']
                        print(f"🏷️  SAN: {san_list}")
                    
                    # 发送真实的HTTP请求
                    http_request = f"GET / HTTP/1.1\r\n"
                    http_request += f"Host: {domain}\r\n"
                    http_request += "User-Agent: TestClient/1.0\r\n"
                    http_request += "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\r\n"
                    http_request += "Connection: close\r\n"
                    http_request += "\r\n"
                    
                    ssl_socket.send(http_request.encode())
                    
                    # 读取响应
                    response_data = b""
                    while True:
                        try:
                            chunk = ssl_socket.recv(4096)
                            if not chunk:
                                break
                            response_data += chunk
                            if len(response_data) > 10240:  # 限制读取大小
                                break
                        except:
                            break
                    
                    response_text = response_data.decode('utf-8', errors='ignore')
                    lines = response_text.split('\n')
                    if lines:
                        print(f"📨 HTTP响应: {lines[0]}")
                        print(f"📊 响应大小: {len(response_data)} 字节")
                    
                    ssl_socket.close()
                    
                except Exception as e:
                    print(f"❌ SSL/HTTP错误: {e}")
                    proxy_socket.close()
            else:
                print("❌ CONNECT失败")
                proxy_socket.close()
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")

def test_concurrent_real_traffic():
    """测试并发真实流量"""
    print("\n=== 测试并发真实流量 ===")
    
    def worker(url, worker_id):
        try:
            proxies = {
                'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
                'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
            }
            
            print(f"🔄 Worker {worker_id}: 请求 {url}")
            response = requests.get(url, proxies=proxies, timeout=15, verify=False)
            print(f"✅ Worker {worker_id}: {response.status_code} - {len(response.content)} 字节")
            
        except Exception as e:
            print(f"❌ Worker {worker_id}: {e}")
    
    urls = [
        'https://httpbin.org/json',
        'https://httpbin.org/headers',
        'https://httpbin.org/user-agent',
        'https://www.baidu.com',
        'https://api.github.com/zen'
    ]
    
    threads = []
    for i, url in enumerate(urls):
        thread = threading.Thread(target=worker, args=(url, i+1))
        threads.append(thread)
        thread.start()
        time.sleep(0.5)  # 错开启动时间
    
    for thread in threads:
        thread.join()

def test_post_request():
    """测试POST请求"""
    print("\n=== 测试POST请求 ===")
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    test_data = {
        'name': 'PacketCapture',
        'version': '1.0.0',
        'test': True
    }
    
    try:
        print("📤 发送POST请求到 httpbin.org/post")
        response = requests.post(
            'https://httpbin.org/post', 
            json=test_data, 
            proxies=proxies, 
            timeout=10,
            verify=False
        )
        print(f"✅ POST响应: {response.status_code}")
        print(f"📊 响应大小: {len(response.content)} 字节")
        
        # 解析响应
        if response.status_code == 200:
            try:
                json_response = response.json()
                print(f"📨 服务器收到的数据: {json_response.get('json', {})}")
            except:
                print("📨 响应不是JSON格式")
                
    except Exception as e:
        print(f"❌ POST请求失败: {e}")

if __name__ == "__main__":
    print("🚀 开始真实网络流量测试")
    print(f"代理地址: {PROXY_HOST}:{PROXY_PORT}")
    print("=" * 60)
    
    # 等待代理启动
    time.sleep(2)
    
    # 测试真实HTTP请求
    test_real_http_request()
    
    # 测试HTTPS证书
    test_real_https_with_certificate()
    
    # 测试POST请求
    test_post_request()
    
    # 测试并发流量
    test_concurrent_real_traffic()
    
    print("\n" + "=" * 60)
    print("🎯 真实流量测试完成！")
    print("\n请检查PacketCapture日志：")
    print("1. 是否统计到了所有CONNECT请求")
    print("2. 是否统计到了HTTP/HTTPS请求和响应")
    print("3. SSL握手失败时是否正确降级")
    print("4. 证书是否为每个域名正确生成")
    print("5. NetworkTrafficAnalyzer是否正确统计了流量")
