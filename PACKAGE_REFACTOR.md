# 包名重构文档

## 重构概述

将整个项目的包名从 `com.company.sdk` 重构为 `com.dev.tools.capture`。

## 重构内容

### 1. 目录结构变更

**原目录结构:**
```
src/main/kotlin/com/company/sdk/
src/test/kotlin/com/company/sdk/
```

**新目录结构:**
```
src/main/kotlin/com/dev/tools/capture/
src/test/kotlin/com/dev/tools/capture/
```

### 2. 包名变更

所有Kotlin文件中的包声明和导入语句都已更新：

- `package com.company.sdk` → `package com.dev.tools.capture`
- `import com.company.sdk.*` → `import com.dev.tools.capture.*`

### 3. 构建配置更新

**build.gradle.kts 变更:**
```kotlin
// 原配置
group = "com.company.sdk"
mainClass.set("com.company.sdk.example.MainKt")

// 新配置
group = "com.dev.tools.capture"
mainClass.set("com.dev.tools.capture.example.MainKt")
```

### 4. 受影响的模块

以下所有模块的包名都已更新：

- **核心模块**
  - `PacketCaptureSDK.kt` - 主入口类
  - `api/` - API接口
  - `core/` - 核心引擎
  - `model/` - 数据模型

- **功能模块**
  - `protocol/` - 协议处理器
  - `security/` - SSL/TLS安全
  - `mock/` - Mock引擎
  - `fallback/` - 降级策略
  - `config/` - 配置管理
  - `callback/` - 回调管理
  - `exception/` - 异常处理
  - `storage/` - 存储管理

- **示例和测试**
  - `example/` - 示例代码
  - `test/` - 测试代码

## 验证结果

### 编译验证
```bash
./gradlew compileKotlin --no-daemon
# ✅ BUILD SUCCESSFUL - 编译成功，只有少量警告
```

### 测试验证
```bash
./gradlew test --no-daemon
# ✅ BUILD SUCCESSFUL - 所有29个测试通过
```

### 运行验证
```bash
./gradlew run --no-daemon
# ✅ BUILD SUCCESSFUL - 示例程序正常运行
```

## 重构影响

### 正面影响
1. **更清晰的命名空间**: `com.dev.tools.capture` 更好地反映了项目的性质
2. **更好的组织结构**: 符合开发工具的包命名约定
3. **避免冲突**: 避免与公司特定的包名冲突

### 注意事项
1. **向后兼容性**: 这是一个破坏性变更，现有使用旧包名的代码需要更新
2. **文档更新**: 所有文档和示例都需要使用新的包名
3. **发布版本**: 建议作为新的主版本发布

## 新的包结构

```
com.dev.tools.capture/
├── PacketCaptureSDK.kt          # 主入口类
├── api/                         # 公共API接口
│   └── PacketCallback.kt
├── callback/                    # 回调管理
│   └── CallbackManager.kt
├── config/                      # 配置管理
│   ├── ConfigManager.kt
│   ├── ConfigTemplates.kt
│   └── ConfigValidator.kt
├── core/                        # 核心引擎
│   └── PacketCaptureEngine.kt
├── exception/                   # 异常处理
│   ├── CaptureException.kt
│   ├── ErrorCodes.kt
│   ├── MockException.kt
│   ├── NetworkException.kt
│   ├── ProtocolException.kt
│   ├── SslException.kt
│   └── SystemException.kt
├── fallback/                    # 降级策略
│   ├── FallbackManager.kt
│   ├── FallbackStrategy.kt
│   ├── LogAndContinueFallbackStrategy.kt
│   ├── RejectFallbackStrategy.kt
│   ├── TunnelFallbackStrategy.kt
│   └── TunnelHandler.kt
├── mock/                        # Mock引擎
│   ├── MockEngine.kt
│   ├── MockRuleBuilder.kt
│   └── MockTemplates.kt
├── model/                       # 数据模型
│   ├── InstantSerializer.kt
│   ├── MockRule.kt
│   ├── PacketData.kt
│   ├── Protocol.kt
│   ├── ProtocolContext.kt
│   ├── SDKConfig.kt
│   └── SessionInfo.kt
├── protocol/                    # 协议处理器
│   ├── AbstractProtocolHandler.kt
│   ├── ProtocolHandler.kt
│   ├── ProtocolManager.kt
│   ├── http/
│   ├── mqtt/
│   └── websocket/
├── security/                    # SSL/TLS安全
│   ├── CertificateProvider.kt
│   ├── SslConfig.kt
│   └── SslManager.kt
├── storage/                     # 存储管理
└── example/                     # 示例代码
    └── Main.kt
```

## 使用新包名的示例

```kotlin
import com.dev.tools.capture.PacketCaptureSDK
import com.dev.tools.capture.api.PacketCallback
import com.dev.tools.capture.model.*

// 创建SDK实例
val sdk = PacketCaptureSDK()

// 初始化配置
val config = SDKConfig(
    port = 8888,
    protocols = setOf(Protocol.HTTP, Protocol.HTTPS)
)

sdk.initialize(config)
```

## 总结

包名重构已成功完成，所有功能保持不变，测试全部通过。新的包名 `com.dev.tools.capture` 更好地反映了项目作为开发工具的定位。
