#!/usr/bin/env python3
"""
测试手机端场景：没有安装证书时的降级处理
"""

import socket
import ssl
import time
import requests
import threading

# 代理配置
PROXY_HOST = '127.0.0.1'
PROXY_PORT = 8888

def test_mobile_https_without_cert():
    """模拟手机端没有安装证书的HTTPS请求"""
    print("\n=== 模拟手机端HTTPS请求（未安装证书）===")
    
    # 模拟手机浏览器的User-Agent
    mobile_headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    # 测试常见的手机访问网站
    test_urls = [
        'https://www.baidu.com',
        'https://m.taobao.com',
        'https://httpbin.org/get',
        'https://api.github.com/zen'
    ]
    
    for url in test_urls:
        try:
            print(f"\n📱 手机请求: {url}")
            
            # 模拟手机端的SSL验证行为（通常会验证证书）
            response = requests.get(
                url, 
                proxies=proxies, 
                headers=mobile_headers,
                timeout=15,
                verify=False,  # 模拟手机端SSL验证失败后的行为
                allow_redirects=True
            )
            
            print(f"✅ 请求成功: {response.status_code}")
            print(f"📊 响应大小: {len(response.content)} 字节")
            print(f"🔗 Content-Type: {response.headers.get('Content-Type', 'N/A')}")
            
        except requests.exceptions.SSLError as e:
            print(f"🔒 SSL错误（预期）: {e}")
            print("💡 这种情况下应该自动降级到隧道模式")
            
        except requests.exceptions.ProxyError as e:
            print(f"🔄 代理错误: {e}")
            print("💡 检查是否正确降级到隧道模式")
            
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        
        time.sleep(2)

def test_raw_ssl_handshake():
    """测试原始SSL握手，模拟手机端SSL验证失败"""
    print("\n=== 测试原始SSL握手（模拟证书验证失败）===")
    
    test_domains = [
        'httpbin.org',
        'www.google.com',
        'www.baidu.com'
    ]
    
    for domain in test_domains:
        try:
            print(f"\n🔐 测试域名: {domain}")
            
            # 连接到代理
            proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            proxy_socket.settimeout(10)
            proxy_socket.connect((PROXY_HOST, PROXY_PORT))
            
            # 发送CONNECT请求
            connect_request = f"CONNECT {domain}:443 HTTP/1.1\r\n"
            connect_request += f"Host: {domain}:443\r\n"
            connect_request += "User-Agent: Mobile-Safari/604.1\r\n"
            connect_request += "Proxy-Connection: keep-alive\r\n"
            connect_request += "\r\n"
            
            print(f"📤 发送CONNECT请求")
            proxy_socket.send(connect_request.encode())
            
            # 读取代理响应
            response = proxy_socket.recv(1024).decode()
            print(f"📨 代理响应: {response.strip()}")
            
            if "200" in response:
                print("✅ CONNECT成功")
                
                # 尝试SSL握手，模拟证书验证
                try:
                    # 创建严格的SSL上下文（模拟手机端的证书验证）
                    ssl_context = ssl.create_default_context()
                    ssl_context.check_hostname = True
                    ssl_context.verify_mode = ssl.CERT_REQUIRED
                    
                    # 这里应该会失败，因为我们的自签名证书不被信任
                    ssl_socket = ssl_context.wrap_socket(proxy_socket, server_hostname=domain)
                    print("✅ SSL握手成功（意外）")
                    ssl_socket.close()
                    
                except ssl.SSLError as e:
                    print(f"🔒 SSL验证失败（预期）: {e}")
                    print("💡 这种情况下应该触发降级到隧道模式")
                    
                except Exception as e:
                    print(f"❌ SSL握手异常: {e}")
                    
            else:
                print("❌ CONNECT失败")
                
            proxy_socket.close()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
        
        time.sleep(1)

def test_concurrent_mobile_requests():
    """测试并发手机请求"""
    print("\n=== 测试并发手机请求 ===")
    
    def mobile_worker(worker_id, url):
        try:
            mobile_headers = {
                'User-Agent': f'Mobile-Worker-{worker_id}/1.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
            }
            
            proxies = {
                'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
                'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
            }
            
            print(f"📱 Worker {worker_id}: 请求 {url}")
            response = requests.get(
                url, 
                proxies=proxies, 
                headers=mobile_headers,
                timeout=15,
                verify=False
            )
            print(f"✅ Worker {worker_id}: {response.status_code} - {len(response.content)} 字节")
            
        except Exception as e:
            print(f"❌ Worker {worker_id}: {e}")
    
    urls = [
        'https://httpbin.org/json',
        'https://httpbin.org/headers',
        'https://httpbin.org/user-agent',
        'https://www.baidu.com'
    ]
    
    threads = []
    for i, url in enumerate(urls):
        thread = threading.Thread(target=mobile_worker, args=(i+1, url))
        threads.append(thread)
        thread.start()
        time.sleep(0.3)  # 错开启动时间
    
    for thread in threads:
        thread.join()

def test_mobile_app_requests():
    """模拟手机APP的网络请求"""
    print("\n=== 模拟手机APP网络请求 ===")
    
    app_headers = {
        'User-Agent': 'TestApp/1.0.0 (iOS 15.0; iPhone13,2)',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-App-Version': '1.0.0',
        'X-Platform': 'iOS'
    }
    
    proxies = {
        'http': f'http://{PROXY_HOST}:{PROXY_PORT}',
        'https': f'http://{PROXY_HOST}:{PROXY_PORT}'
    }
    
    # 模拟APP的API请求
    api_requests = [
        ('GET', 'https://httpbin.org/get'),
        ('POST', 'https://httpbin.org/post', {'app_data': 'test', 'user_id': 12345}),
        ('GET', 'https://api.github.com/users/octocat'),
    ]
    
    for method, url, *data in api_requests:
        try:
            print(f"\n📲 APP请求: {method} {url}")
            
            if method == 'GET':
                response = requests.get(
                    url, 
                    proxies=proxies, 
                    headers=app_headers,
                    timeout=10,
                    verify=False
                )
            elif method == 'POST':
                response = requests.post(
                    url, 
                    json=data[0] if data else {},
                    proxies=proxies, 
                    headers=app_headers,
                    timeout=10,
                    verify=False
                )
            
            print(f"✅ APP响应: {response.status_code}")
            print(f"📊 响应大小: {len(response.content)} 字节")
            
        except Exception as e:
            print(f"❌ APP请求失败: {e}")
        
        time.sleep(1)

if __name__ == "__main__":
    print("📱 开始手机端场景测试")
    print(f"代理地址: {PROXY_HOST}:{PROXY_PORT}")
    print("🎯 测试目标：验证没有安装证书时的自动降级功能")
    print("=" * 60)
    
    # 等待代理启动
    time.sleep(2)
    
    # 测试手机端HTTPS请求
    test_mobile_https_without_cert()
    
    # 测试原始SSL握手
    test_raw_ssl_handshake()
    
    # 测试并发手机请求
    test_concurrent_mobile_requests()
    
    # 测试手机APP请求
    test_mobile_app_requests()
    
    print("\n" + "=" * 60)
    print("🎯 手机端场景测试完成！")
    print("\n请检查PacketCapture日志：")
    print("1. 🔄 SSL握手失败时是否自动降级")
    print("2. ✅ 降级后手机端是否能正常访问")
    print("3. 🚫 是否没有出现管道异常错误")
    print("4. 📊 CONNECT请求是否被正确统计")
    print("5. 🔗 隧道模式是否正常工作")
